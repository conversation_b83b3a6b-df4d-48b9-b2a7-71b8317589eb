<?php

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Detectar ambiente e usar configuração apropriada
$isLocal = (
    $_SERVER['HTTP_HOST'] === 'localhost' ||
    $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
    strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
);

if ($isLocal && file_exists("config_local.php")) {
    include_once("config_local.php");
} else {
    include_once("config.php");
}

include_once("func.php");

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $is_admin = $_SESSION['user_is_admin'];
    $pousada_id = $_SESSION['user_pousada_id'];
    
    // Verificar e inicializar dados padrão se necessário
    verificarEInicializarDadosPadrao($conn, $pousada_id);
    
} else {
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

// Funções para gerenciar usuários (cadastrar, alterar, excluir)
function cadastrarUsuario($conn, $nome, $senha, $is_admin) {
    $senhaHash = password_hash($senha, PASSWORD_DEFAULT);
    $stmt = prepareAndExecute($conn, "INSERT INTO usuarios (nome, senha, is_admin) VALUES (?, ?, ?)", 'ssi', $nome, $senhaHash, $is_admin);
    return $stmt->affected_rows > 0;
}

function alterarUsuario($conn, $id, $nome, $senha, $is_admin) {
    $senhaHash = password_hash($senha, PASSWORD_DEFAULT);
    $stmt = prepareAndExecute($conn, "UPDATE usuarios SET nome = ?, senha = ?, is_admin = ? WHERE id = ?", 'ssii', $nome, $senhaHash, $is_admin, $id);
    return $stmt->affected_rows > 0;
}

function excluirUsuario($conn, $id) {
	// Verifica se é o último administrador
	$stmt = $conn->prepare("SELECT COUNT(*) as admin_count FROM usuarios WHERE is_admin = 1");
	$stmt->execute();
	$result = $stmt->get_result();
	$adminCount = $result->fetch_assoc()['admin_count'];
	
	// Obtém o status do usuário que está sendo excluído
	$stmt = $conn->prepare("SELECT is_admin FROM usuarios WHERE id = ?");
	$stmt->bind_param('i', $id);
	$stmt->execute();
	$result = $stmt->get_result();
	$user = $result->fetch_assoc();
	
	if ($adminCount > 1 || !$user['is_admin']) {
		// Permite a exclusão se não for o último administrador
		$stmt = prepareAndExecute($conn, "DELETE FROM usuarios WHERE id = ?", 'i', $id);
		return $stmt->affected_rows > 0;
	} else {
		// Bloqueia a exclusão se for o último administrador
		return false;
	}
}


if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $action = $_POST['acao'] ?? '';
    $nome = $_POST['nome'] ?? '';
    $senha = $_POST['senha'] ?? '';
    $id = $_POST['id'] ?? 0;
    $is_admin = isset($_POST['is_admin']) ? 1 : 0;

    if ($action == "cadastrar" && !empty($nome) && !empty($senha)) {
        $success = cadastrarUsuario($conn, $nome, $senha, $is_admin);
        $message = $success ? "Usuário cadastrado com sucesso." : "Erro ao cadastrar usuário.";
    } elseif ($action == "alterar" && !empty($nome) && !empty($senha) && !empty($id)) {
        // CORREÇÃO: Usar $is_admin ao invés de $is_Admin
        $success = alterarUsuario($conn, $id, $nome, $senha, $is_admin);
        $message = $success ? "Usuário alterado com sucesso." : "Erro ao alterar usuário.";
    } elseif ($action == "excluir" && !empty($id)) {
        $success = excluirUsuario($conn, $id);
        $message = $success ? "Usuário excluído com sucesso." : "Erro ao excluir usuário.";
    } else {
        $message = "Dados incompletos.";
    }
}

$usuarios = $conn->query("SELECT id, nome, is_admin FROM usuarios");

?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="css/bootstrap.min.css" rel="stylesheet">
	<div class="titulo ">
    <title>Hospeda Max</title>
    <link rel="stylesheet" href="custom/css/form_fnrh.css">
    <link rel="stylesheet" href="custom/css/admin-financeiro.css">
    <link rel="stylesheet" href="custom/css/tooltip-system.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css">

    <!-- CSS de cores personalizadas baseado na sessão -->
    <style>
    :root {
        <?php
        // Definir variáveis CSS baseadas nas cores da sessão
        if (isset($_SESSION['pousada_cores'])) {
            $cores = $_SESSION['pousada_cores'];
        } else {
            // Cores padrão se não estiver logado
            $cores = [
                'primaria' => '#28a745',
                'clara' => '#a8e6c1',
                'escura' => '#1e7e34',
                'rgb' => '40, 167, 69'
            ];
        }
        ?>
        --cor-primaria: <?php echo $cores['primaria']; ?>;
        --cor-clara: <?php echo $cores['clara']; ?>;
        --cor-escura: <?php echo $cores['escura']; ?>;
        --cor-rgb: <?php echo $cores['rgb']; ?>;
    }

    /* Aplicar as variáveis CSS */
    body {
        background-color: rgba(var(--cor-rgb), 0.1) !important;
        font-family: Arial, sans-serif;
    }

    .navbar.navbar-expand-lg.navbar-light {
        background: linear-gradient(135deg, var(--cor-primaria) 0%, var(--cor-clara) 100%) !important;
        border-bottom: none !important;
    }

    .navbar .nav-link {
        color: white !important;
    }

    .navbar .nav-link:hover {
        color: var(--cor-escura) !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
        border-radius: 4px;
    }

    .form-group button,
    .btn-success {
        background-color: var(--cor-primaria) !important;
        border-color: var(--cor-primaria) !important;
    }

    .form-group button:hover,
    .btn-success:hover {
        background-color: var(--cor-escura) !important;
        border-color: var(--cor-escura) !important;
    }

    .livre {
        background-color: var(--cor-clara) !important;
    }

    .alert-success {
        background-color: var(--cor-clara) !important;
        border-color: var(--cor-primaria) !important;
        color: var(--cor-escura) !important;
    }
    </style>
</head>
<body>
   <nav class="navbar navbar-expand-lg navbar-light bg-light no-print">
        <div class="container-fluid">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                <li class="dropdown-item"><a class="nav-link active" aria-current="page" href="index.php">Home</a></li>
                    
                    <!-- Menu Hospedes -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarHospedesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Hospedes
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarHospedesDropdown">
                            <li><a class="dropdown-item" href="#" onclick="abrirModalNovoHospedeGlobal(); return false;">Novo Hospede</a></li>
                            <li><a class="dropdown-item" href="?page=listar">Listar Hospedes</a></li>
                            <li><a class="dropdown-item" href="?page=procurar">Procurar Hospedes</a></li>
                            <li><a class="dropdown-item" href="?page=mapa_uh">Mapa de Quartos</a></li>
                        </ul>
                    </li>

                    <!-- Menu Financeiro -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarFinanceiroDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Financeiro
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarFinanceiroDropdown">
                            <li><a class="dropdown-item" href="?page=contas">Contas a Pagar/Receber</a></li>
                            <li><a class="dropdown-item" href="?page=caixa_diario">Caixa Diário</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="?page=dashboard_financeiro">Dashboard</a></li>
                            <li><a class="dropdown-item" href="?page=relatorio_fluxo_caixa">Relatório de Fluxo de Caixa</a></li>
                            <li><a class="dropdown-item" href="?page=relatorio_categorias">Relatório por Categorias</a></li>
                            <li><a class="dropdown-item" href="?page=relatorio_resultados">Demonstrativo de Resultados</a></li>
                        </ul>
                    </li>
                            
                    
                    <!-- Menu Utilitarios -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarUtilitariosDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Utilitarios
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarUtilitariosDropdown">
                            <li><a class="dropdown-item" href="?page=admin_usuarios">Administração de Usuários</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="?page=configurar_cores"><i class="bi bi-palette"></i> Configurar Cores</a></li>
                            <li><a class="dropdown-item" href="?page=mudar_logotipo"><i class="bi bi-image"></i> Mudar Logotipo</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="?page=formas_pagamento">Formas de Pagamento</a></li>
                            <li><a class="dropdown-item" href="?page=categorias_financeiras">Categorias Financeiras</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="?page=ler_qrcod">Ler QRCode</a></li>
                            <?php if ($_SESSION['user_pousada_id'] == 0): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="?page=admin_pousadas">Administração de Pousadas</a></li>
                                <li><a class="dropdown-item" href="?page=admin_sql">Editor SQL</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                            

					<li class="nav-item"><a class="nav-link" href="logout.php">Sair</a></li>

                </ul>
            </div>
        </div>
    </nav>
	

	
	<!-- Adicione a imagem aqui
     nomear a imagem do cliente como: 
	 src="Logo_id_00000.png"
	 alt="Logo [nome do cliente]"

	-->
    <div class="container text-center mt-4 ">
        <img src="<?php echo $_SESSION['pousada_logo'] ?? 'img/Logo_Bom_Viver.png'; ?>" alt="Logo" class="img-fluid no-print">
    </div>
	
	<div class="container">
        <div class="row">
            <div class="col mt-5">
                <?php
                switch (@$_REQUEST["page"]) {
                    case "listar": include("hospedes_listar.php"); break;
                    case "procurar": include("hospedes_procurar.php"); break;

                    case "configurar_cores": include("configurar_cores_include.php"); break;
                    case "mudar_logotipo": include("mudar_logotipo.php"); break;
                    case "salvar": include("hospedes_salvar.php"); break;
                    case "reservas": include("reservas_listar.php"); break;
                    case "reservas_salvar": include("reservas_salvar.php"); break;
				    case "admin_usuarios": include("admin_usuarios.php"); break;
					case "ler_qrcod": include("ler_qrcode.php"); break;
                    case "mapa_uh": include("mapa_uh.php"); break;
                    
                    // Módulo Financeiro
                    case "contas": include("financeiro/contas.php"); break;
                    case "contas_salvar": include("financeiro/contas_salvar.php"); break;
                    case "categorias_financeiras": include("financeiro/categorias_financeiras.php"); break;
                    case "categorias_financeiras_salvar": include("financeiro/categorias_financeiras_salvar.php"); break;
                    case "formas_pagamento": include("financeiro/formas_pagamento.php"); break;
                    case "formas_pagamento_salvar": include("financeiro/formas_pagamento_salvar.php"); break;
                    case "caixa_diario": include("financeiro/caixa_diario.php"); break;
                    case "caixa_diario_salvar": include("financeiro/caixa_diario_salvar.php"); break;
                    case "caixa_diario_detalhes": include("financeiro/caixa_diario_detalhes.php"); break;
                    case "relatorio_fluxo_caixa": include("financeiro/relatorio_fluxo_caixa.php"); break;
                    case "relatorio_categorias": include("financeiro/relatorio_categorias.php"); break;
                    case "relatorio_resultados": include("financeiro/relatorio_resultados.php"); break;
                    case "dashboard_financeiro": include("financeiro/dashboard_financeiro.php"); break;
                    case "admin_pousadas": include("admin_pousadas.php"); break;
                    case "admin_sql": include("admin_sql.php"); break;
                    
                    default:
                        print "<center><h1>Bem vindos!</h1></center>";
                }
                ?>
            </div>
        </div>
    </div>

    <!-- Modal Global de Cadastro de Hóspede -->
    <div class="modal fade" id="modalGlobalNovoHospede" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cadastrar Novo Hóspede</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalGlobalNovoHospedeContainer">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p>Carregando formulário...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>

    <!-- Scripts necessários para validação de CPF -->
    <script src="custom/js/func.js"></script>

    <!-- Script Global para Modal de Novo Hóspede -->
    <script>
    // Classe para gerenciar modal global de novo hóspede
    class ModalGlobalNovoHospede {
        constructor() {
            this.modal = null;
            this.container = null;
            this.init();
        }

        init() {
            this.modal = document.getElementById('modalGlobalNovoHospede');
            this.container = document.getElementById('modalGlobalNovoHospedeContainer');
        }

        abrir() {
            if (!this.modal || !this.container) {
                console.error('Modal global não encontrado');
                return;
            }

            // Mostrar modal
            const modalInstance = new bootstrap.Modal(this.modal);
            modalInstance.show();

            // Carregar formulário via AJAX
            this.carregarFormulario();
        }

        carregarFormulario() {
            // Mostrar loading
            this.container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p>Carregando formulário...</p>
                </div>
            `;

            // Carregar formulário via AJAX
            fetch('carregar_formulario_hospede.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.html) {
                    this.container.innerHTML = data.html;

                    // Aguardar o DOM estar pronto e configurar formulário
                    setTimeout(() => {
                        this.configurarFormulario();
                    }, 300); // Tempo para garantir que o DOM esteja pronto
                } else {
                    this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário.</div>';
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário.</div>';
            });
        }

        configurarFormulario() {
            const form = this.container.querySelector('form');
            if (!form) return;

            // Configurar envio via AJAX
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.salvarHospede(form);
            });

            // Configurar validações e formatações
            this.configurarValidacoes(form);
        }

        configurarValidacoes(form) {
            // Configurar validação completa de CPF (baseada no modal do mapa_uh)
            const cpfField = form.querySelector('input[name="cpf"]');
            if (cpfField) {
                // Buscar ou criar div de feedback
                let feedbackDiv = document.getElementById('cpf-feedback');
                if (!feedbackDiv) {
                    feedbackDiv = document.createElement('div');
                    feedbackDiv.id = 'cpf-feedback';
                    feedbackDiv.style.fontSize = '0.875em';
                    feedbackDiv.style.marginTop = '0.25rem';
                    feedbackDiv.style.display = 'block';
                    feedbackDiv.style.minHeight = '1.2em';
                    cpfField.parentNode.appendChild(feedbackDiv);
                } else {
                    // Se já existe, garantir que esteja visível
                    feedbackDiv.style.display = 'block';
                    feedbackDiv.style.fontSize = '0.875em';
                    feedbackDiv.style.marginTop = '0.25rem';
                    feedbackDiv.style.minHeight = '1.2em';
                }

                // Criar referência à instância para usar dentro do event listener
                const modalInstance = this;

                cpfField.addEventListener('input', function() {
                    // Verificação de segurança
                    if (!this.value) {
                        this.value = '';
                        return;
                    }

                    // Formatação (igual ao mapa_uh)
                    let cpf = this.value.replace(/\D/g, ''); // Remove caracteres não numéricos
                    if (cpf.length <= 11) {
                        cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                        cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                        cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
                    }
                    this.value = cpf;

                    const submitButton = form.querySelector('button[type="submit"]');

                    // Verifica se o CPF está incompleto
                    if (cpf.length < 14 && cpf.length > 0) {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                        this.title = 'CPF incompleto. Por favor, digite todos os dígitos.';
                        // CPF incompleto - desabilitar botão
                        if (feedbackDiv) {
                            feedbackDiv.innerHTML = '<b>CPF incompleto</b>. Digite todos os dígitos.';
                            feedbackDiv.style.color = 'orange';
                            feedbackDiv.style.display = 'block';
                        }
                        if (submitButton) {
                            submitButton.disabled = true;
                            submitButton.textContent = 'CPF Incompleto';
                            submitButton.classList.add('btn-secondary');
                            submitButton.classList.remove('btn-primary', 'btn-danger');
                        }
                    } else if (cpf.length === 14) { // Verifica se o CPF está completo
                        // Usar função global se disponível, senão usar da instância
                        const cpfValido = (typeof validarCPF === 'function') ? validarCPF(cpf) : modalInstance.validarCPF(cpf);

                        if (cpfValido) {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                            this.title = '';
                            // CPF válido, mas ainda precisa verificar duplicidade
                            if (feedbackDiv) {
                                feedbackDiv.innerHTML = '<b>Verificando disponibilidade...</b>';
                                feedbackDiv.style.color = 'orange';
                                feedbackDiv.style.display = 'block';
                            }
                            // Verificar duplicidade - usar função global se disponível
                            if (typeof verificarCPFDuplicado === 'function') {
                                verificarCPFDuplicado(cpf, this);
                            } else {
                                modalInstance.verificarCPFDuplicado(cpf, this);
                            }
                        } else {
                            this.classList.remove('is-valid');
                            this.classList.add('is-invalid');
                            this.title = 'CPF inválido. Verifique a digitação.';
                            // CPF inválido - desabilitar botão
                            if (feedbackDiv) {
                                feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
                                feedbackDiv.style.color = 'red';
                                feedbackDiv.style.display = 'block';
                            }
                            if (submitButton) {
                                submitButton.disabled = true;
                                submitButton.textContent = 'CPF Inválido';
                                submitButton.classList.add('btn-secondary');
                                submitButton.classList.remove('btn-primary', 'btn-danger');
                            }
                        }
                    } else if (cpf.length === 0) {
                        // Campo vazio - remover classes de validação
                        this.classList.remove('is-valid', 'is-invalid');
                        this.title = '';
                        if (feedbackDiv) {
                            feedbackDiv.innerHTML = '';
                        }
                        // Permitir envio se outros campos obrigatórios estiverem preenchidos
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.textContent = 'Registrar';
                            submitButton.classList.remove('btn-secondary', 'btn-danger');
                            submitButton.classList.add('btn-primary');
                        }
                    }
                }.bind(this));
            }

            // Configurar cálculo de idade
            const nascField = form.querySelector('input[name="nasc"]');
            const idadeField = form.querySelector('input[name="idade"]');
            if (nascField && idadeField) {
                nascField.addEventListener('change', function() {
                    if (this.value) {
                        const hoje = new Date();
                        const nascimento = new Date(this.value);
                        let idade = hoje.getFullYear() - nascimento.getFullYear();
                        const mes = hoje.getMonth() - nascimento.getMonth();
                        if (mes < 0 || (mes === 0 && hoje.getDate() < nascimento.getDate())) {
                            idade--;
                        }
                        idadeField.value = idade >= 0 ? idade : '';
                    }
                });
            }
        }

        validarCPF(cpf) {
            // Verificação de segurança
            if (!cpf || typeof cpf !== 'string') return false;

            cpf = cpf.replace(/[^\d]+/g, '');
            if (cpf === '') return false;

            // Elimina CPFs inválidos conhecidos
            if (cpf.length !== 11 ||
                cpf === "00000000000" ||
                cpf === "11111111111" ||
                cpf === "22222222222" ||
                cpf === "33333333333" ||
                cpf === "44444444444" ||
                cpf === "55555555555" ||
                cpf === "66666666666" ||
                cpf === "77777777777" ||
                cpf === "88888888888" ||
                cpf === "99999999999")
                return false;

            // Valida 1º dígito
            let add = 0;
            for (let i = 0; i < 9; i++)
                add += parseInt(cpf.charAt(i)) * (10 - i);
            let rev = 11 - (add % 11);
            if (rev === 10 || rev === 11)
                rev = 0;
            if (rev !== parseInt(cpf.charAt(9)))
                return false;

            // Valida 2º dígito
            add = 0;
            for (let i = 0; i < 10; i++)
                add += parseInt(cpf.charAt(i)) * (11 - i);
            rev = 11 - (add % 11);
            if (rev === 10 || rev === 11)
                rev = 0;
            if (rev !== parseInt(cpf.charAt(10)))
                return false;

            return true;
        }

        verificarCPFDuplicado(cpf, inputElement) {
            const submitButton = document.querySelector('button[type="submit"]');
            const feedbackDiv = document.getElementById('cpf-feedback');

            // Verificação de segurança
            if (!cpf || typeof cpf !== 'string') {
                console.error('CPF inválido passado para verificarCPFDuplicado:', cpf);
                return;
            }

            // Só verificar duplicidade se CPF for válido e tiver 11 dígitos
            const cpfLimpo = cpf.replace(/\D/g, '');
            if (cpfLimpo.length < 11) {
                if (feedbackDiv) {
                    feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
                }
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.textContent = 'CPF Incompleto';
                    submitButton.classList.add('btn-secondary');
                    submitButton.classList.remove('btn-primary');
                }
                return;
            }

            if (!this.validarCPF(cpf)) {
                if (feedbackDiv) {
                    feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
                }
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.textContent = 'CPF Inválido';
                    submitButton.classList.add('btn-secondary');
                    submitButton.classList.remove('btn-primary');
                }
                return;
            }

            // Construir URL da requisição
            let url = 'verificar_cpf.php?cpf=' + encodeURIComponent(cpf);
            fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.existe) {
                    // CPF duplicado - desabilitar botão e mostrar erro
                    inputElement.classList.remove('is-valid');
                    inputElement.classList.add('is-invalid');
                    inputElement.title = 'CPF já cadastrado para: ' + (data.nome_hospede || '');
                    if (feedbackDiv) {
                        feedbackDiv.innerHTML = '<b>CPF já cadastrado para: ' + (data.nome_hospede || '') + '</b>';
                        feedbackDiv.style.color = 'red';
                        feedbackDiv.style.display = 'block';
                    }
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.textContent = 'CPF Duplicado - Não é possível registrar';
                        submitButton.classList.add('btn-danger');
                        submitButton.classList.remove('btn-primary', 'btn-secondary');
                    }
                } else {
                    // CPF disponível - habilitar botão
                    inputElement.classList.remove('is-invalid');
                    inputElement.classList.add('is-valid');
                    inputElement.title = '';
                    if (feedbackDiv) {
                        feedbackDiv.innerHTML = '<b>CPF disponível</b>';
                        feedbackDiv.style.color = 'green';
                        feedbackDiv.style.display = 'block';
                    }
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.textContent = 'Registrar';
                        submitButton.classList.add('btn-primary');
                        submitButton.classList.remove('btn-danger', 'btn-secondary');
                    }
                }
            })
            .catch(error => {
                console.error('Erro ao verificar CPF:', error);
                // Em caso de erro, permitir o envio (fallback)
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = 'Registrar';
                    submitButton.classList.add('btn-primary');
                    submitButton.classList.remove('btn-danger', 'btn-secondary');
                }
            });
        }

        salvarHospede(form) {
            const formData = new FormData(form);

            fetch('hospedes_salvar.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Fechar modal
                    const modalInstance = bootstrap.Modal.getInstance(this.modal);
                    modalInstance.hide();

                    // Mostrar sucesso e redirecionar
                    alert('Hóspede cadastrado com sucesso!');

                    // Redirecionar para lista com busca pelo nome
                    window.location.href = `index.php?page=listar&tipo_pesquisa=nome&termo_pesquisa=${encodeURIComponent(data.hospede_nome || '')}`;
                } else {
                    alert('Erro: ' + (data.message || 'Não foi possível cadastrar o hóspede'));
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao salvar hóspede. Tente novamente.');
            });
        }
    }

    // Instância global
    let modalGlobalNovoHospede;

    // Inicializar quando página carregar
    document.addEventListener('DOMContentLoaded', function() {
        modalGlobalNovoHospede = new ModalGlobalNovoHospede();
    });

    // Função global para abrir modal
    function abrirModalNovoHospedeGlobal() {
        if (modalGlobalNovoHospede) {
            modalGlobalNovoHospede.abrir();
        } else {
            console.error('Modal global não inicializado');
            alert('Erro: Modal não disponível. Recarregue a página e tente novamente.');
        }
    }
    </script>

    <script>
        // Verificar se CSS de cores precisa ser atualizado
        document.addEventListener('DOMContentLoaded', function() {
            // Aplicar cores da sessão (exceto na página de configurar cores)
            const isConfigurarCores = window.location.search.includes('page=configurar_cores');
            const coresAtualizadas = window.location.search.includes('cores_atualizadas=');
            console.log('URL atual:', window.location.href);
            console.log('É página configurar cores?', isConfigurarCores);
            console.log('Cores foram atualizadas?', coresAtualizadas);

            // Sempre aplicar cores da sessão (exceto na página de configurar cores)
            // OU se as cores foram recém atualizadas
            if (!isConfigurarCores || coresAtualizadas) {
                console.log('Aplicando cores da sessão...');
                <?php
                // Debug: verificar cores na sessão
                if (isset($_SESSION['pousada_cores'])) {
                    error_log("Cores na sessão no index.php: " . json_encode($_SESSION['pousada_cores']));
                } else {
                    error_log("Cores NÃO encontradas na sessão no index.php");
                }
                ?>
                <?php if (isset($_SESSION['pousada_cores'])): ?>
                const coresSessao = {
                    primaria: '<?php echo $_SESSION['pousada_cores']['primaria']; ?>',
                    clara: '<?php echo $_SESSION['pousada_cores']['clara']; ?>',
                    escura: '<?php echo $_SESSION['pousada_cores']['escura']; ?>',
                    rgb: '<?php echo $_SESSION['pousada_cores']['rgb']; ?>'
                };
                console.log('Cores da sessão encontradas:', coresSessao);
                <?php else: ?>
                console.log('Cores da sessão NÃO encontradas!');
                <?php endif; ?>

                <?php if (isset($_SESSION['pousada_cores'])): ?>

                const root = document.documentElement;
                root.style.setProperty('--cor-primaria', coresSessao.primaria);
                root.style.setProperty('--cor-clara', coresSessao.clara);
                root.style.setProperty('--cor-escura', coresSessao.escura);
                root.style.setProperty('--cor-rgb', coresSessao.rgb);

                console.log('Cores aplicadas na home:', coresSessao);
                <?php endif; ?>
            } else {
                console.log('Página de configurar cores - cores NÃO aplicadas');
            }

            // Forçar aplicação das cores após um pequeno delay (para redirecionamentos)
            setTimeout(function() {
                if (!isConfigurarCores || coresAtualizadas) {
                    <?php if (isset($_SESSION['pousada_cores'])): ?>
                    const coresSessao = {
                        primaria: '<?php echo $_SESSION['pousada_cores']['primaria']; ?>',
                        clara: '<?php echo $_SESSION['pousada_cores']['clara']; ?>',
                        escura: '<?php echo $_SESSION['pousada_cores']['escura']; ?>',
                        rgb: '<?php echo $_SESSION['pousada_cores']['rgb']; ?>'
                    };

                    const root = document.documentElement;
                    root.style.setProperty('--cor-primaria', coresSessao.primaria);
                    root.style.setProperty('--cor-clara', coresSessao.clara);
                    root.style.setProperty('--cor-escura', coresSessao.escura);
                    root.style.setProperty('--cor-rgb', coresSessao.rgb);

                    console.log('Cores reaplicadas após delay:', coresSessao);
                    <?php endif; ?>
                }

                // Se cores foram atualizadas, limpar URL após aplicar
                if (coresAtualizadas) {
                    // Remover parâmetro cores_atualizadas da URL
                    const url = new URL(window.location);
                    url.searchParams.delete('cores_atualizadas');
                    window.history.replaceState({}, document.title, url.pathname + url.search);
                    console.log('Parâmetro cores_atualizadas removido da URL');
                }
            }, 100);

            // Verificar se cores foram atualizadas em outras abas
            const timestampCoresSalvo = localStorage.getItem('css_cores_timestamp');
            if (timestampCoresSalvo) {
                const ultimaVerificacao = localStorage.getItem('ultima_verificacao_cores') || '0';
                if (timestampCoresSalvo !== ultimaVerificacao) {
                    localStorage.setItem('ultima_verificacao_cores', timestampCoresSalvo);

                    // Buscar cores atuais da sessão via AJAX
                    fetch('get_cores_sessao.php')
                        .then(response => response.json())
                        .then(cores => {
                            if (cores.success) {
                                // Aplicar cores instantaneamente
                                const root = document.documentElement;
                                root.style.setProperty('--cor-primaria', cores.primaria);
                                root.style.setProperty('--cor-clara', cores.clara);
                                root.style.setProperty('--cor-escura', cores.escura);
                                root.style.setProperty('--cor-rgb', cores.rgb);

                                console.log('Cores sincronizadas de outra aba:', cores);
                            }
                        })
                        .catch(() => {
                            // Se AJAX falhar, recarregar página como fallback
                            location.reload();
                        });
                }
            }

            // Verificar se logo precisa ser atualizado
            const timestampLogoSalvo = localStorage.getItem('logo_timestamp');
            if (timestampLogoSalvo) {
                const logosNaPagina = document.querySelectorAll('img[src*="img/logo"], img[src*="Logo_Bom_Viver"], img[alt="Logo"], img[src*="pousada_"]');
                logosNaPagina.forEach(function(img) {
                    const urlAtual = img.src;
                    const timestampAtual = urlAtual.includes('?v=') ? urlAtual.split('?v=')[1] : '0';

                    if (timestampLogoSalvo !== timestampAtual) {
                        img.src = img.src.split('?')[0] + '?v=' + timestampLogoSalvo;
                    }
                });
            }
        });
    </script>
</body>
</html>

