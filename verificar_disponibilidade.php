<?php
session_start();

try {
    // Verificar se o usuário está logado
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {
        echo json_encode(['erro' => 'Usuário não autenticado']);
        exit;
    }
    
    include_once("config.php");
    
    /**
     * Função adaptada do conflitoclaude.php para verificar conflito de período completo
     * Mantém compatibilidade com os parâmetros existentes do projeto
     */
    function verificarConflitoReservaPeriodo($data_entrada, $hora_entrada, $data_saida, $hora_saida, $uh, $pousada_id, $reserva_id_excluir = null) {
        global $conn;
        
        // Combina data e hora
        $datetime_entrada = $data_entrada . ' ' . $hora_entrada;
        $datetime_saida = $data_saida . ' ' . $hora_saida;
        
        // Query para buscar reservas conflitantes
        $sql = "SELECT id, dataentrada, horaentrada, datasaida, horasaida, hospede_id 
                FROM reservas 
                WHERE pousada_id = ? 
                AND uh = ? 
                AND dataentrada IS NOT NULL 
                AND datasaida IS NOT NULL";
        
        if ($reserva_id_excluir) {
            $sql .= " AND id != ?";
        }
        
        $stmt = $conn->prepare($sql);
        
        if ($reserva_id_excluir) {
            $stmt->bind_param("issi", $pousada_id, $uh, $reserva_id_excluir);
        } else {
            $stmt->bind_param("is", $pousada_id, $uh);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        $conflitos = [];
        
        while ($reserva = $result->fetch_assoc()) {
            $entrada_existente = $reserva['dataentrada'] . ' ' . ($reserva['horaentrada'] ?: '13:00:00');
            $saida_existente = $reserva['datasaida'] . ' ' . ($reserva['horasaida'] ?: '12:00:00');

            $ts_entrada_nova = strtotime($datetime_entrada);
            $ts_saida_nova = strtotime($datetime_saida);
            $ts_entrada_existente = strtotime($entrada_existente);
            $ts_saida_existente = strtotime($saida_existente);

            // Verifica sobreposição de períodos
            if ($ts_entrada_nova < $ts_saida_existente && $ts_saida_nova > $ts_entrada_existente) {
                $conflitos[] = [
                    'id' => $reserva['id'],
                    'hospede_id' => $reserva['hospede_id'],
                    'entrada' => $entrada_existente,
                    'saida' => $saida_existente,
                    'dataentrada' => $reserva['dataentrada'],
                    'datasaida' => $reserva['datasaida'],
                    'horaentrada' => $reserva['horaentrada'],
                    'horasaida' => $reserva['horasaida']
                ];
            }
        }
        
        return [
            'conflito' => !empty($conflitos),
            'detalhes' => $conflitos
        ];
    }
    
    // Obter dados do POST - mantendo compatibilidade com chamadas existentes
    $uh = $_POST['uh'] ?? '';
    $dataEntrada = $_POST['dataEntrada'] ?? '';
    $horaEntrada = $_POST['horaEntrada'] ?? '13:00';
    $dataSaida = $_POST['dataSaida'] ?? $dataEntrada;
    $horaSaida = $_POST['horaSaida'] ?? '12:00';
    $reserva_id_excluir = isset($_POST['reserva_id_excluir']) ? intval($_POST['reserva_id_excluir']) : null;
    $pousada_id = $_SESSION['user_pousada_id'];
    
    // Log para diagnóstico (apenas quando necessário)
    if (isset($_POST['debug'])) {
        error_log("Verificando disponibilidade - UH: $uh, Entrada: $dataEntrada $horaEntrada, Saída: $dataSaida $horaSaida, ID a excluir: " . ($reserva_id_excluir ?? 'nenhum'));
        error_log("POST recebido: " . print_r($_POST, true));
    }
    
    // Validar dados
    if (empty($uh) || empty($dataEntrada)) {
        echo json_encode(['erro' => 'Dados incompletos']);
        exit;
    }
    
    // Converter para o formato do banco de dados
    $dataEntradaObj = new DateTime($dataEntrada);
    $dataEntradaFormatada = $dataEntradaObj->format('Y-m-d');
    
    // Se não foi informada data de saída, assumir mesmo dia
    if (empty($dataSaida)) {
        $dataSaidaFormatada = $dataEntradaFormatada;
    } else {
        $dataSaidaObj = new DateTime($dataSaida);
        $dataSaidaFormatada = $dataSaidaObj->format('Y-m-d');
    }
    
    // Validar ID de reserva para exclusão (se fornecido)
    if ($reserva_id_excluir) {
        $reserva_id_excluir = filter_var($reserva_id_excluir, FILTER_VALIDATE_INT);
        if ($reserva_id_excluir === false) {
            echo json_encode(['erro' => 'ID de reserva inválido', 'id_recebido' => $_POST['reserva_id_excluir']]);
            exit;
        }
        error_log("Excluindo reserva ID: $reserva_id_excluir da verificação de conflitos");
    }
    
    // Usar a função robusta do conflitoclaude.php
    $resultado = verificarConflitoReservaPeriodo(
        $dataEntradaFormatada, 
        $horaEntrada, 
        $dataSaidaFormatada, 
        $horaSaida, 
        $uh, 
        $pousada_id, 
        $reserva_id_excluir
    );
    
    // Adaptar saída para formato esperado pelas chamadas JavaScript existentes
    if ($resultado['conflito']) {
        $conflito = $resultado['detalhes'][0]; // Primeiro conflito encontrado
        
        // Usar os campos de data originais em vez dos formatados
        $dataEntrada = new DateTime($conflito['dataentrada']);
        $dataSaida = new DateTime($conflito['datasaida']);
        
        // Verificar se são datas diferentes
        $sameDay = $dataEntrada->format('Y-m-d') === $dataSaida->format('Y-m-d');
        
        if ($sameDay) {
            // Mesmo dia: "Reservada em 24/06 de 13:00 às 15:00"
            $data = $dataEntrada->format('d/m');
            $horaEntrada = substr($conflito['horaentrada'], 0, 5); // Remove segundos
            $horaSaida = substr($conflito['horasaida'], 0, 5);
            $mensagem = "Reservada em {$data} de {$horaEntrada} às {$horaSaida}";
        } else {
            // Datas diferentes: "Reservada entre 24/06 e 25/06"
            $dataIni = $dataEntrada->format('d/m');
            $dataFim = $dataSaida->format('d/m');
            $mensagem = "Reservada entre {$dataIni} e {$dataFim}";
        }
        
        echo json_encode([
            'disponivel' => false,
            'reserva_id' => $conflito['id'],
            'id_excluido' => $reserva_id_excluir,
            'mensagem' => $mensagem
        ]);
    } else {
        echo json_encode(['disponivel' => true]);
    }
    
} catch (Exception $e) {
    // Log do erro para debug
    error_log("Erro em verificar_disponibilidade.php (NOVA VERSÃO): " . $e->getMessage());
    // Retornar erro genérico para o cliente
    echo json_encode(['erro' => 'Erro interno no servidor', 'mensagem' => $e->getMessage()]);
    exit;
}
?>