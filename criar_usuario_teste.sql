-- Script para criar usuário de teste no ambiente local
-- Execute no phpMyAdmin se tiver problemas de login

-- Criar usuário de teste simples
INSERT INTO usuarios (nome, senha, email, pousada_id, is_admin) VALUES 
('Admin Local', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 1, 1);

-- Senha: password

-- Verificar se foi criado
SELECT id, nome, email, pousada_id, is_admin FROM usuarios WHERE email = '<EMAIL>';

-- Dad<PERSON> para login:
-- Email: <EMAIL>
-- <PERSON>ha: password
