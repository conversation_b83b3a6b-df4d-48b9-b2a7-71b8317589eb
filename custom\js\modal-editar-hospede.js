class ModalEditarHospede {
    constructor() {
        this.modal = null;
        this.container = null;
        this.init();
    }

    init() {
        this.modal = document.getElementById('editarHospedeModal');
        this.container = document.getElementById('formularioEditarHospedeContainer');
        if (!this.modal || !this.container) {
            console.error('Modal de editar hóspede não encontrado');
            return;
        }
    }

    carregarFormularioEdicao(hospedeId) {
        if (!this.modal || !this.container) {
            console.error('Modal não inicializado');
            return;
        }

        // Atualizar cabeçalho do modal
        const modalTitle = this.modal.querySelector('.modal-title');
        if (modalTitle) {
            modalTitle.textContent = 'Editar Hóspede';
        }

        // Mostrar modal com loading
        const modalInstance = new bootstrap.Modal(this.modal);
        modalInstance.show();

        // Mostrar loading
        this.container.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Carregando...</span>
                </div>
                <p>Carregando formulário de edição...</p>
            </div>
        `;

        // Carregar formulário via AJAX
        fetch('carregar_formulario_edicao_hospede.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `hospede_id=${encodeURIComponent(hospedeId)}`
        })
            .then(response => response.json())
            .then((data) => {
                if (data.success) {
                    if (this.container && data.html) {
                        this.container.innerHTML = data.html;
                        // Configurar formulário após carregar
                        setTimeout(() => {
                            this.setupFormulario();
                            // Configurar funcionalidade de impressão e QR code
                            this.setupPrintFunctionality(data.hospede_id);
                        }, 100);
                    }
                } else {
                    if (this.container) {
                        this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário de edição.</div>';
                    }
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                if (this.container) {
                    this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário de edição.</div>';
                }
            });
    }

    setupFormulario() {
        const form = document.getElementById('formEditarHospede');
        if (!form) {
            console.error('Formulário de edição não encontrado');
            return;
        }

        // Configurar validação e eventos
        this.setupFormValidation();

        // Adicionar evento de submit
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.salvarEdicaoHospede(form);
        });
    }

    setupFormValidation() {
        // Configurar validação de CPF se necessário
        const cpfField = document.querySelector('input[name="cpf"]');
        if (cpfField) {
            cpfField.addEventListener('blur', () => {
                this.validarCPF(cpfField);
            });
        }

        // Configurar cálculo automático de idade
        const nascField = document.querySelector('input[name="nasc"]');
        const idadeField = document.querySelector('input[name="idade"]');
        if (nascField && idadeField) {
            nascField.addEventListener('change', () => {
                this.calcularIdade(nascField, idadeField);
            });
        }
    }

    validarCPF(cpfField) {
        // Adicionar div de feedback se não existir
        let feedbackDiv = document.getElementById('cpf-feedback');
        if (!feedbackDiv) {
            feedbackDiv = document.createElement('div');
            feedbackDiv.id = 'cpf-feedback';
            feedbackDiv.style.fontSize = '0.875em';
            feedbackDiv.style.marginTop = '0.25rem';
            cpfField.parentNode.appendChild(feedbackDiv);
        }

        cpfField.addEventListener('input', function () {
            // Formatação
            let value = this.value.replace(/\D/g, '');
            if (value.length <= 11) {
                value = value.replace(/(\d{3})(\d)/, '$1.$2');
                value = value.replace(/(\d{3})(\d)/, '$1.$2');
                value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
                this.value = value;
            }

            // Validação
            const cpfLimpo = this.value.replace(/\D/g, '');
            const submitButton = document.querySelector('button[type="submit"]');
            const hospedeId = document.querySelector('input[name="id"]')?.value || 0;

            if (cpfLimpo.length === 0) {
                // CPF vazio - permitir
                this.classList.remove('is-valid', 'is-invalid');
                feedbackDiv.innerHTML = '';
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = 'Salvar Alterações';
                    submitButton.classList.remove('btn-secondary');
                    submitButton.classList.add('btn-primary');
                }
            } else if (cpfLimpo.length < 11) {
                // CPF incompleto
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
                feedbackDiv.innerHTML = '<span style="color: red;"><b>CPF incompleto</b></span>';
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.textContent = 'CPF Incompleto';
                    submitButton.classList.add('btn-secondary');
                    submitButton.classList.remove('btn-primary');
                }
            } else if (cpfLimpo.length === 11) {
                // CPF completo - validar
                if (this.validarCPFAlgoritmo(cpfLimpo)) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                    feedbackDiv.innerHTML = '<span style="color: orange;"><b>Verificando disponibilidade...</b></span>';

                    // Verificar duplicidade
                    this.verificarCPFDuplicado(cpfLimpo, this, feedbackDiv, submitButton, hospedeId);
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                    feedbackDiv.innerHTML = '<span style="color: red;"><b>CPF inválido</b></span>';
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.textContent = 'CPF Inválido';
                        submitButton.classList.add('btn-secondary');
                        submitButton.classList.remove('btn-primary');
                    }
                }
            }
        }.bind(this));

        // Adicionar métodos de validação ao contexto
        cpfField.validarCPFAlgoritmo = this.validarCPFAlgoritmo;
        cpfField.verificarCPFDuplicado = this.verificarCPFDuplicado;
    }

    validarCPFAlgoritmo(cpf) {
        cpf = cpf.replace(/[^\d]+/g, '');
        if (cpf === '') return false;

        // Elimina CPFs inválidos conhecidos
        if (cpf.length !== 11 ||
            cpf === "00000000000" ||
            cpf === "11111111111" ||
            cpf === "22222222222" ||
            cpf === "33333333333" ||
            cpf === "44444444444" ||
            cpf === "55555555555" ||
            cpf === "66666666666" ||
            cpf === "77777777777" ||
            cpf === "88888888888" ||
            cpf === "99999999999")
            return false;

        // Valida 1º dígito
        let add = 0;
        for (let i = 0; i < 9; i++)
            add += parseInt(cpf.charAt(i)) * (10 - i);
        let rev = 11 - (add % 11);
        if (rev === 10 || rev === 11)
            rev = 0;
        if (rev !== parseInt(cpf.charAt(9)))
            return false;

        // Valida 2º dígito
        add = 0;
        for (let i = 0; i < 10; i++)
            add += parseInt(cpf.charAt(i)) * (11 - i);
        rev = 11 - (add % 11);
        if (rev === 10 || rev === 11)
            rev = 0;
        if (rev !== parseInt(cpf.charAt(10)))
            return false;

        return true;
    }

    verificarCPFDuplicado(cpf, inputElement, feedbackDiv, submitButton, hospedeId = 0) {
        const url = `verificar_cpf.php?cpf=${encodeURIComponent(cpf)}&hospede_id=${hospedeId}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.existe) {
                    inputElement.classList.remove('is-valid');
                    inputElement.classList.add('is-invalid');
                    feedbackDiv.innerHTML = `<span style="color: red;"><b>CPF já cadastrado para: ${data.nome_hospede}</b></span>`;
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.textContent = 'CPF Duplicado';
                        submitButton.classList.add('btn-secondary');
                        submitButton.classList.remove('btn-primary');
                    }
                } else {
                    inputElement.classList.remove('is-invalid');
                    inputElement.classList.add('is-valid');
                    feedbackDiv.innerHTML = '<span style="color: green;"><b>CPF válido</b></span>';
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.textContent = 'Salvar Alterações';
                        submitButton.classList.remove('btn-secondary');
                        submitButton.classList.add('btn-primary');
                    }
                }
            })
            .catch(error => {
                console.error('Erro ao verificar CPF:', error);
                feedbackDiv.innerHTML = '<span style="color: orange;"><b>Erro ao verificar CPF</b></span>';
            });
    }

    calcularIdade(nascField, idadeField) {
        const dataNasc = new Date(nascField.value);
        const hoje = new Date();

        if (dataNasc && dataNasc <= hoje) {
            let idade = hoje.getFullYear() - dataNasc.getFullYear();
            const mesAtual = hoje.getMonth();
            const mesNasc = dataNasc.getMonth();

            if (mesAtual < mesNasc || (mesAtual === mesNasc && hoje.getDate() < dataNasc.getDate())) {
                idade--;
            }

            idadeField.value = idade;
        }
    }

    salvarEdicaoHospede(form) {
        const formData = new FormData(form);

        // Adicionar flag AJAX para o servidor identificar o contexto
        formData.append('ajax', '1');

        // Log dos dados enviados
        console.log('Enviando dados para salvar:');
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }

        fetch('hospedes_salvar.php', {
            method: 'POST',
            body: formData
        })
            .then(response => {
                console.log('Status da resposta:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Resposta bruta do servidor:', text);
                if (!text.trim()) {
                    throw new Error('Resposta vazia do servidor');
                }

                let data;
                try {
                    data = JSON.parse(text);
                    console.log('Dados JSON processados:', data);
                } catch (e) {
                    console.error('Erro ao processar JSON:', e, 'Texto recebido:', text);
                    throw new Error('Resposta inválida do servidor: ' + text);
                }

                if (data.success) {
                    alert('Hóspede editado com sucesso!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editarHospedeModal'));
                    if (modal) {
                        modal.hide();
                    }
                    setTimeout(() => location.reload(), 500);
                } else {
                    alert('Erro ao editar hóspede: ' + (data.message || 'Erro desconhecido'));
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao salvar: ' + error.message);
            });
    }

    setupPrintFunctionality(hospedeId) {
        // Carregar biblioteca QR Code se não estiver carregada
        this.loadQRCodeLibrary(() => {
            // Criar QR Code com ID do hóspede
            this.createQRCode(hospedeId);
            // Configurar botão de impressão
            this.setupPrintButton();
        });
    }

    loadQRCodeLibrary(callback) {
        // Verificar se a biblioteca já está carregada
        if (typeof QRCode !== 'undefined') {
            callback();
            return;
        }

        // Carregar biblioteca QR Code
        const script = document.createElement('script');
        script.src = 'instascan/qrcode.js';
        script.onload = callback;
        script.onerror = () => {
            console.error('Erro ao carregar biblioteca QR Code');
        };
        document.head.appendChild(script);
    }

    createQRCode(hospedeId) {
        const qrContainer = document.getElementById('qrcode-hospede');
        if (qrContainer && typeof QRCode !== 'undefined') {
            // Limpar container antes de criar novo QR code
            qrContainer.innerHTML = '';
            try {
                const qrcode = new QRCode(qrContainer, {
                    text: hospedeId.toString(),
                    width: 80,
                    height: 80,
                    colorDark: "black",
                    colorLight: "white",
                    correctLevel: QRCode.CorrectLevel.H
                });
                console.log('QR Code criado para hóspede ID:', hospedeId);
            } catch (error) {
                console.error('Erro ao criar QR Code:', error);
                qrContainer.innerHTML = '<p class="text-muted"><small>Erro ao gerar QR Code</small></p>';
            }
        }
    }

    setupPrintButton() {
        const printButton = document.getElementById('print-button-hospede');
        if (printButton) {
            printButton.addEventListener('click', () => {
                this.imprimirHospede();
            });
        }
    }

    imprimirHospede() {
        // Usar a mesma função de impressão do sistema
        window.print();
    }
}

// Exportar para window global
window.ModalEditarHospede = ModalEditarHospede;
