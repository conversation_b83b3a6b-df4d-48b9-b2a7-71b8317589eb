<?php
// Configurações de locale para português brasileiro
setlocale(LC_ALL, 'pt_BR', 'pt_BR.utf-8', 'portuguese');
date_default_timezone_set('America/Sao_Paulo');

// Detectar ambiente automaticamente
$isLocal = (
    $_SERVER['HTTP_HOST'] === 'localhost' ||
    $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
    $_SERVER['HTTP_HOST'] === 'pousada.local' ||
    strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0 ||
    strpos($_SERVER['HTTP_HOST'], '127.0.0.1:') === 0 ||
    strpos($_SERVER['SERVER_NAME'], 'localhost') !== false ||
    $_SERVER['SERVER_ADDR'] === '127.0.0.1'
);

if ($isLocal) {
    // Configurações para ambiente local (XAMPP)
    define('HOST', 'localhost');
    define('USER', 'root');
    define('PASS', ''); // XAMPP vem sem senha por padrão
    define('BASE', 'claudio2_pousada');
} else {
    // Configurações para servidor de produção
    define('HOST', 'localhost');
    define('USER', 'claudio2_pousada');
    define('PASS', 'CSC@pou159753');
    define('BASE', 'claudio2_pousada');
}

// Conexão com o banco de dados
$conn = new mysqli(HOST, USER, PASS, BASE);
$conn->set_charset("utf8");

if ($conn->connect_error) {
    die("Erro de conexão: " . $conn->connect_error);
}

// Função para debug (só funciona localmente)
function debugLocal($message) {
    global $isLocal;
    if ($isLocal) {
        echo "<script>console.log('🔍 DEBUG: " . addslashes($message) . "');</script>\n";
    }
}

// Log do ambiente atual (apenas para debug)
if ($isLocal) {
    debugLocal("Conectado ao ambiente LOCAL");
} else {
    // Comentário silencioso para produção
    // Ambiente: SERVIDOR
}

?>