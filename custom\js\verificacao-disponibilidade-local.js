/**
 * Função TypeScript baseada na lógica PHP de verificar_disponibilidade.php
 * Permite verificação local sem requisições ao servidor
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
class VerificacaoDisponibilidadeLocal {
    constructor() {
        this.CACHE_VALIDO_MS = 30000; // 30 segundos
        this.reservasCache = new Map();
        this.ultimaAtualizacao = null;
    }
    /**
     * Carrega reservas do servidor para cache local
     */
    carregarReservas() {
        return __awaiter(this, arguments, void 0, function* (uh = null, forcarAtualizacao = false) {
            const agora = new Date().getTime();
            if (!forcarAtualizacao && this.ultimaAtualizacao &&
                (agora - this.ultimaAtualizacao) < this.CACHE_VALIDO_MS) {
                return;
            }
            try {
                const formData = new FormData();
                formData.append('acao', 'carregar_reservas');
                if (uh) {
                    formData.append('uh', uh);
                }
                const response = yield fetch('custom/includes/carregar_reservas.php', {
                    method: 'POST',
                    body: formData
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = yield response.json();
                if (data.sucesso && data.reservas) {
                    // Limpar cache se for atualização forçada
                    if (forcarAtualizacao) {
                        this.reservasCache.clear();
                    }
                    // Organizar reservas por UH
                    data.reservas.forEach((reserva) => {
                        if (!this.reservasCache.has(reserva.uh)) {
                            this.reservasCache.set(reserva.uh, []);
                        }
                        this.reservasCache.get(reserva.uh).push(reserva);
                    });
                    this.ultimaAtualizacao = agora;
                }
                else {
                    console.warn('Falha ao carregar reservas:', data.erro || 'Resposta inválida');
                }
            }
            catch (error) {
                console.error('Erro ao carregar reservas:', error);
                throw error;
            }
        });
    }
    /**
     * Verifica conflito de período - baseado na lógica PHP
     */
    verificarConflitoLocal(dataEntrada, horaEntrada, dataSaida, horaSaida, uh, reservaIdExcluir = null) {
        const reservasUH = this.reservasCache.get(uh) || [];

        // Debug apenas quando necessário
        if (window.debugVerificacao) {
            console.log('verificarConflitoLocal - Parâmetros:', { dataEntrada, horaEntrada, dataSaida, horaSaida, uh, reservaIdExcluir });
            console.log('Reservas em cache para UH', uh, ':', reservasUH);
        }

        // Combinar data e hora
        const datetimeEntrada = `${dataEntrada} ${horaEntrada}`;
        const datetimeSaida = `${dataSaida} ${horaSaida}`;
        const tsEntradaNova = new Date(datetimeEntrada).getTime();
        const tsSaidaNova = new Date(datetimeSaida).getTime();
        // Validar timestamps
        if (isNaN(tsEntradaNova) || isNaN(tsSaidaNova)) {
            console.error('Datas inválidas fornecidas:', { dataEntrada, horaEntrada, dataSaida, horaSaida });
            return { conflito: false, detalhes: [] };
        }
        if (tsEntradaNova >= tsSaidaNova) {
            console.warn('Data/hora de entrada deve ser anterior à saída');
            return {
                conflito: true,
                detalhes: [{
                    id: -1, // ID especial para erro de validação
                    hospede_nome: 'Erro de Validação',
                    data_entrada_formatada: this.formatarData(dataEntrada),
                    data_saida_formatada: this.formatarData(dataSaida),
                    dataentrada: dataEntrada, // Adicionar campo original
                    datasaida: dataSaida, // Adicionar campo original
                    horaentrada: horaEntrada,
                    horasaida: horaSaida
                }]
            };
        }
        const conflitos = [];
        for (const reserva of reservasUH) {
            // Pular se for a própria reserva sendo editada
            if (reservaIdExcluir && reserva.id === reservaIdExcluir) {
                if (window.debugVerificacao) {
                    console.log('Pulando reserva sendo editada:', reserva.id);
                }
                continue;
            }
            const entradaExistente = `${reserva.dataentrada} ${reserva.horaentrada || '13:00:00'}`;
            const saidaExistente = `${reserva.datasaida} ${reserva.horasaida || '12:00:00'}`;
            const tsEntradaExistente = new Date(entradaExistente).getTime();
            const tsSaidaExistente = new Date(saidaExistente).getTime();
            // Verificar se as datas existentes são válidas
            if (isNaN(tsEntradaExistente) || isNaN(tsSaidaExistente)) {
                console.warn('Reserva com datas inválidas ignorada:', reserva);
                continue;
            }
            // Verificar sobreposição: entrada_nova < saida_existente E saida_nova > entrada_existente
            const temSobreposicao = tsEntradaNova < tsSaidaExistente && tsSaidaNova > tsEntradaExistente;

            if (window.debugVerificacao) {
                console.log('Verificação de sobreposição para reserva', reserva.id, ':', {
                    temSobreposicao,
                    entradaExistente,
                    saidaExistente,
                    datetimeEntrada,
                    datetimeSaida
                });
            }

            if (temSobreposicao) {
                if (window.debugVerificacao) {
                    console.log('CONFLITO DETECTADO com reserva:', reserva.id);
                }
                conflitos.push({
                    id: reserva.id,
                    hospede_nome: reserva.hospede_nome,
                    data_entrada_formatada: this.formatarData(reserva.dataentrada),
                    data_saida_formatada: this.formatarData(reserva.datasaida),
                    dataentrada: reserva.dataentrada, // Adicionar campo original
                    datasaida: reserva.datasaida, // Adicionar campo original
                    horaentrada: reserva.horaentrada,
                    horasaida: reserva.horasaida
                });
            }
        }
        return {
            conflito: conflitos.length > 0,
            detalhes: conflitos
        };
    }
    /**
     * Formata mensagem de conflito de forma mais concisa
     */
    formatarMensagemConflito(conflito, uh) {
        // Usar os campos de data originais (YYYY-MM-DD) em vez dos formatados
        const dataEntrada = new Date(conflito.dataentrada);
        const dataSaida = new Date(conflito.datasaida);
        // Verificar se são datas diferentes
        const sameDay = dataEntrada.toDateString() === dataSaida.toDateString();
        if (sameDay) {
            // Mesmo dia: "Reservada em 24/06 de 13:00 às 15:00"
            const data = dataEntrada.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
            const horaEntrada = conflito.horaentrada.substring(0, 5); // Remove segundos
            const horaSaida = conflito.horasaida.substring(0, 5);
            return `Reservada em ${data} de ${horaEntrada} às ${horaSaida}`;
        }
        else {
            // Datas diferentes: "Reservada entre 24/06 e 25/06"
            const dataIni = dataEntrada.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
            const dataFim = dataSaida.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
            return `Reservada entre ${dataIni} e ${dataFim}`;
        }
    }
    /**
     * Função principal para verificação híbrida (local + servidor)
     */
    verificarDisponibilidade(uh_1, dataEntrada_1, horaEntrada_1, dataSaida_1, horaSaida_1) {
        return __awaiter(this, arguments, void 0, function* (uh, dataEntrada, horaEntrada, dataSaida, horaSaida, reservaIdExcluir = null) {
            // Validar parâmetros obrigatórios
            if (!uh || !dataEntrada) {
                throw new Error('UH e data de entrada são obrigatórios');
            }
            try {
                // 1. Tentar verificação local primeiro
                yield this.carregarReservas(uh);
                const resultadoLocal = this.verificarConflitoLocal(dataEntrada, horaEntrada, dataSaida, horaSaida, uh, reservaIdExcluir);
                // 2. Se houver conflito local, retornar imediatamente
                if (resultadoLocal.conflito && resultadoLocal.detalhes.length > 0) {
                    const conflito = resultadoLocal.detalhes[0];
                    // Caso especial: erro de validação de datas
                    if (conflito.id === -1) {
                        return {
                            disponivel: false,
                            mensagem: 'Data/hora de entrada deve ser anterior à saída',
                            fonte: 'validacao'
                        };
                    }
                    return {
                        disponivel: false,
                        reserva_id: conflito.id,
                        mensagem: this.formatarMensagemConflito(conflito, uh),
                        fonte: 'local'
                    };
                }
                // 3. Se não houver conflito local, fazer verificação no servidor para confirmar
                return yield this.verificarNoServidor(uh, dataEntrada, horaEntrada, dataSaida, horaSaida, reservaIdExcluir);
            }
            catch (error) {
                console.error('Erro na verificação de disponibilidade:', error);
                return {
                    disponivel: false,
                    mensagem: `Erro na verificação: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
                    fonte: 'local-fallback'
                };
            }
        });
    }
    /**
     * Verificação no servidor
     */
    verificarNoServidor(uh, dataEntrada, horaEntrada, dataSaida, horaSaida, reservaIdExcluir) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const formData = new FormData();
                formData.append('uh', uh);
                formData.append('dataEntrada', dataEntrada);
                formData.append('horaEntrada', horaEntrada);
                formData.append('dataSaida', dataSaida);
                formData.append('horaSaida', horaSaida);
                if (reservaIdExcluir !== null) {
                    formData.append('reserva_id_excluir', reservaIdExcluir.toString());
                }
                const response = yield fetch('verificar_disponibilidade.php', {
                    method: 'POST',
                    body: formData
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const resultado = yield response.json();
                resultado.fonte = 'servidor';
                // Atualizar cache se servidor encontrou conflito que o local não viu
                if (!resultado.disponivel) {
                    yield this.carregarReservas(uh, true); // Forçar atualização do cache
                }
                return resultado;
            }
            catch (error) {
                console.error('Erro na verificação do servidor:', error);
                // Retornar resultado local em caso de erro do servidor
                return {
                    disponivel: true,
                    mensagem: 'Verificação local apenas (servidor indisponível)',
                    fonte: 'local-fallback'
                };
            }
        });
    }
    /**
     * Formatar data para exibição
     */
    formatarData(data) {
        try {
            const d = new Date(data);
            if (isNaN(d.getTime())) {
                return data; // Retornar string original se não conseguir converter
            }
            return d.toLocaleDateString('pt-BR');
        }
        catch (error) {
            console.warn('Erro ao formatar data:', data, error);
            return data;
        }
    }
    /**
     * Limpar cache (útil após criar/editar/excluir reservas)
     */
    limparCache() {
        this.reservasCache.clear();
        this.ultimaAtualizacao = null;
    }
    /**
     * Obter estatísticas do cache
     */
    obterEstatisticasCache() {
        let totalReservas = 0;
        this.reservasCache.forEach(reservas => {
            totalReservas += reservas.length;
        });
        return {
            totalUHs: this.reservasCache.size,
            totalReservas,
            ultimaAtualizacao: this.ultimaAtualizacao ? new Date(this.ultimaAtualizacao) : null
        };
    }
}
// Instância global
const verificadorLocal = new VerificacaoDisponibilidadeLocal();
/**
 * Função compatível com o código existente
 */
function verificarDisponibilidadeUHMelhorada(uh, dataEntrada, horaEntrada, callback, reservaIdExcluir, dataSaida, horaSaida) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Ativar debug temporariamente para diagnóstico
            window.debugVerificacao = true;

            console.log('verificarDisponibilidadeUHMelhorada chamada com:', {
                uh, dataEntrada, horaEntrada, dataSaida, horaSaida, reservaIdExcluir
            });

            const resultado = yield verificadorLocal.verificarDisponibilidade(uh, dataEntrada, horaEntrada || '13:00', dataSaida || dataEntrada, horaSaida || '12:00', reservaIdExcluir || null);

            console.log('Resultado da verificação:', resultado);
            callback(resultado, null);

            // Desativar debug
            window.debugVerificacao = false;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
            console.error('Erro na verificação:', errorMessage);
            callback(null, errorMessage);
            window.debugVerificacao = false;
        }
    });
}
// Exportações ES6
export { VerificacaoDisponibilidadeLocal, verificadorLocal, verificarDisponibilidadeUHMelhorada };

// Exportar para window global para compatibilidade com modal
window.verificarDisponibilidadeUHMelhorada = verificarDisponibilidadeUHMelhorada;
