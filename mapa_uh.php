<?php

// Reportar todos os erros do PHP

//error_reporting(E_ALL);

//ini_set('display_errors', 1);



// Check if a session is not already active before starting

if (session_status() == PHP_SESSION_NONE) {

    session_start();

}



// Inclui os arquivos de configuração e funções

if (!function_exists('prepareAndExecute') && !isset($conn)) {

    include_once("config.php");

    include_once("func.php");

}



include_once("custom/includes/mapa_uh_functions.php");



// Função auxiliar para formatar data e hora

function formatarDataHora($data, $hora) {

    if (!$data) return 'N/A';



    $dataFormatada = date('d/m/Y', strtotime($data));



    if ($hora && $hora !== '00:00:00') {

        $horaFormatada = date('H:i', strtotime($hora));

        return $dataFormatada . ' às ' . $horaFormatada;

    }



    return $dataFormatada;

}





// Verify session explicitly with the existing session variables

if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {

    error_log("Sessão inválida no mapa_uh.php");

    echo "<script>window.location.href = 'index.html';</script>";

    return;

}



// Obtém o ID da pousada do usuário logado

$pousada_id = $_SESSION['user_pousada_id'];



// Obtém o mês e ano atual

$mes = isset($_GET['mes']) ? intval($_GET['mes']) : date('n');

$ano = isset($_GET['ano']) ? intval($_GET['ano']) : date('Y');



// Ajusta o mês e ano se estiverem fora dos limites

if ($mes < 1) {

    $mes = 12;

    $ano--;

} elseif ($mes > 12) {

    $mes = 1;

    $ano++;

}



// Calcula o primeiro dia do mês

$primeiroDia = mktime(0, 0, 0, $mes, 1, $ano);

$numeroDias = date('t', $primeiroDia);

$diaSemanaPrimeiroDia = date('w', $primeiroDia); // 0 (Domingo) a 6 (Sábado)



// Consulta as unidades habitacionais (quartos) da pousada

$sql_quartos = "SELECT DISTINCT uh FROM reservas WHERE pousada_id = ? ORDER BY uh";

$stmt_quartos = $conn->prepare($sql_quartos);



if ($stmt_quartos === false) {

    die("Prepare failed: " . $conn->error);

}



$stmt_quartos->bind_param('i', $pousada_id);



if ($stmt_quartos->execute() === false) {

    die("Execute failed: " . $stmt_quartos->error);

}



$result_quartos = $stmt_quartos->get_result();

$quartos = array();

while ($row = $result_quartos->fetch_assoc()) {

    $quartos[] = $row['uh'];

}



$stmt_quartos->close();



// Busca as reservas do mês para determinar a ocupação

$sql_reservas = "SELECT r.id, r.hospede_id, r.uh, r.numacomp, r.dataentrada, r.datasaida, r.horaentrada, r.horasaida, h.nome AS hospede_nome

                 FROM reservas r

                 LEFT JOIN hospedes h ON r.hospede_id = h.id

                 WHERE r.pousada_id = ? AND

                 (

                     (YEAR(r.dataentrada) = ? AND MONTH(r.dataentrada) = ?) OR

                     (YEAR(r.datasaida) = ? AND MONTH(r.datasaida) = ?) OR

                     (r.dataentrada <= LAST_DAY(?) AND r.datasaida >= ?)

                 )";



$stmt_reservas = $conn->prepare($sql_reservas);



if ($stmt_reservas === false) {

    die("Prepare failed: " . $conn->error);

}



// Simplified date for checking the month

$data_mes = date('Y-m-d', mktime(0, 0, 0, $mes, 1, $ano));



$stmt_reservas->bind_param('iisssss', 

    $pousada_id, 

    $ano, 

    $mes, 

    $ano, 

    $mes, 

    $data_mes,

    $data_mes

);



if ($stmt_reservas->execute() === false) {

    die("Execute failed: " . $stmt_reservas->error);

}



$result_reservas = $stmt_reservas->get_result();

$reservas = array();



while ($row = $result_reservas->fetch_assoc()) {

    $reservas[] = $row;

}



$stmt_reservas->close();





?>







<!DOCTYPE html>

<html lang="pt-BR">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Mapa de UH</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <link rel="stylesheet" href="custom/css/form_fnrh.css">

    <link rel="stylesheet" href="custom/css/mapa_uh.css">

    <link rel="stylesheet" href="custom/css/valida_data_anterior.css">

    <link rel="stylesheet" href="custom/css/responsive-fixes.css">

</head>

<body class="debug-breakpoint">

    <div class="container-fluid">

        <h1 class="text-center mb-4">Mapa de UH</h1>

        

        <!-- Botão Home acima do mapa -->

        <div class="text-center mb-3">

            <button onclick="location.href='index.php';" class="btn btn-success">Home</button>

        </div>



        <!-- Navegação entre meses -->

        <div class="d-flex justify-content-between align-items-center mb-3">

            <a href="mapa_uh.php?mes=<?php echo $mes - 1; ?>&ano=<?php echo $ano; ?>" class="btn btn-secondary">

                <i class="bi bi-arrow-left"></i> 

            </a>

            <h2><?php 

            $meses = [

                1 => 'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 

                'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'

            ];

            $mesAno = $meses[$mes] . ' de ' . $ano;

            echo $mesAno;

            ?></h2>

            <a href="mapa_uh.php?mes=<?php echo $mes + 1; ?>&ano=<?php echo $ano; ?>" class="btn btn-secondary">

                <i class="bi bi-arrow-right"></i>

            </a>

        </div>



        <!-- Tabela do calendário -->

        <div class="table-responsive">

            <table class="table table-bordered table-hover">

                <thead>

                    <tr>

                        <th>UH</th>

                        <?php

                        // Array com as letras dos dias da semana (0=Domingo, 1=Segunda, etc.)

                        $diasSemana = ['D', 'S', 'T', 'Q', 'Q', 'S', 'S'];



                        // Generate column headers for each day of the month

                        for ($diaColuna = 1; $diaColuna <= $numeroDias; $diaColuna++):

                            $data_formatada = date('Y-m-d', mktime(0, 0, 0, $mes, $diaColuna, $ano));

                            $diaSemana = date('w', mktime(0, 0, 0, $mes, $diaColuna, $ano));

                            $letraDia = $diasSemana[$diaSemana];

                            

                            // Adicionar classe para fins de semana

                            $classFimSemana = ($diaSemana == 0 || $diaSemana == 6) ? 'fim-semana' : '';

                            

                            echo "<th class='dia-cabecalho $classFimSemana'>

                                    <div class='letra-dia'>$letraDia</div>

                                    <div class='numero-dia'>$diaColuna</div>

                                  </th>";

                        endfor;

                        ?>

                    </tr>

                </thead>

                <tbody>

                    <?php

                    // Função para gerar número com cor

                    function gerarNumeroComCor($numero) {

                        if ($numero == 0) return '';



                        $classe_cor = '';

                        switch ($numero) {

                            case 1:

                                $classe_cor = 'numero-reserva-1';

                                break;

                            case 2:

                                $classe_cor = 'numero-reserva-2';

                                break;

                            case 3:

                                $classe_cor = 'numero-reserva-3';

                                break;

                            default:

                                $classe_cor = 'numero-reserva-4plus';

                                break;

                        }



                        return "<span class='numero-reserva $classe_cor' title='$numero reserva(s)'>$numero</span>";

                    }



                    // Iterate through each UH (quarto)

                    foreach ($quartos as $quarto):

                        echo "<tr>";

                        echo "<td>" . htmlspecialchars($quarto) . "</td>";



                        // Iterate through each day of the month

                        for ($diaAtual = 1; $diaAtual <= $numeroDias; $diaAtual++):

                            $data_formatada = date('Y-m-d', mktime(0, 0, 0, $mes, $diaAtual, $ano));

                            $data_brasileira = date('d/m/Y', mktime(0, 0, 0, $mes, $diaAtual, $ano));

                            

                            $hoje = date('Y-m-d');

                            $data_formatada = date('Y-m-d', mktime(0, 0, 0, $mes, $diaAtual, $ano));

                            $eh_data_passada = ($data_formatada < $hoje);

                            $eh_hoje = ($data_formatada == $hoje);

                            

                            $ocupacao = isUhOcupada($quarto, $data_formatada, $reservas);

                            

                            $classe = '';

                            $icones = '';

                            $reserva_data = null;

                            

                            // Obter contagem de reservas

                            $total_reservas = isset($ocupacao['total_reservas']) ? $ocupacao['total_reservas'] : 0;



                            switch ($ocupacao['status']) {

                                case 'empty': // Empty all day

                                    $classe = 'livre';

                                    $icones = '<i class="bi bi-check-circle-fill text-success" title="Livre o dia todo"></i>';

                                    break;

                                case 'morning': // Occupied in morning, empty in afternoon

                                    $classe = 'ocupado-manha cell-clickable';

                                    $icones = gerarNumeroComCor($total_reservas);

                                    $reserva_data = $ocupacao['reserva'];

                                    break;

                                case 'afternoon': // Empty in morning, occupied in afternoon

                                    $classe = 'ocupado-tarde cell-clickable';

                                    $icones = gerarNumeroComCor($total_reservas);

                                    $reserva_data = $ocupacao['reserva'];

                                    break;

                                case 'double_occupied': // Occupied all day by two different reservations

                                    $classe = 'ocupado-duplo cell-clickable';

                                    $icones = gerarNumeroComCor($total_reservas);

                                    $reserva_checkout = $ocupacao['reserva_checkout'];

                                    $reserva_checkin = $ocupacao['reserva_checkin'];

                                    break;

                                case 'full': // Occupied all day

                                    $classe = 'ocupado cell-clickable';

                                    $icones = gerarNumeroComCor($total_reservas);

                                    $reserva_data = $ocupacao['reserva'];

                                    break;

                                case 'maintenance': // UH em manutenção

                                    $classe = 'manutencao cell-clickable';

                                    $icones = '<i class="bi bi-tools text-warning" title="Em manutenção"></i>';

                                    break;

                                case 'cleaning': // UH em limpeza

                                    $classe = 'limpeza cell-clickable';

                                    $icones = '<i class="bi bi-droplet text-info" title="Em limpeza"></i>';

                                    break;

                                default:

                                    $classe = 'livre';

                                    $icones = '<i class="bi bi-question-circle text-muted" title="Status desconhecido"></i>';

                                    break;

                            }



                            // Adicionar classe para datas passadas

                            if ($eh_data_passada) {

                                $classe .= ' data-passada';

                            }



                            // Adicionar classe para a data atual (hoje)

                            if ($eh_hoje) {

                                $classe .= ' data-hoje';

                            }

                            

                            // Escapar todos os dados para JavaScript

                            echo "<td class='$classe' data-bs-toggle='modal' data-bs-target='#reservaModal' 

                                     data-uh='" . htmlspecialchars($quarto, ENT_QUOTES) . "' 

                                     data-data='" . htmlspecialchars($data_brasileira, ENT_QUOTES) . "'";

                            

                            // Adicionar status para todos os casos

                            echo " data-status='" . htmlspecialchars($ocupacao['status'], ENT_QUOTES) . "'";



                            if ($ocupacao['status'] == 'double_occupied') {

                                // Para o caso de dupla ocupação, incluir dados de ambas as reservas

                                echo " data-reserva-checkout-id='" . htmlspecialchars($reserva_checkout['id'], ENT_QUOTES) . "'

                                       data-hospede-checkout-nome='" . htmlspecialchars($reserva_checkout['hospede_nome'], ENT_QUOTES) . "'

                                       data-checkout-entrada='" . htmlspecialchars(formatarDataHora($reserva_checkout['dataentrada'], $reserva_checkout['horaentrada']), ENT_QUOTES) . "'

                                       data-checkout-saida='" . htmlspecialchars(formatarDataHora($reserva_checkout['datasaida'], $reserva_checkout['horasaida']), ENT_QUOTES) . "'";



                                echo " data-reserva-checkin-id='" . htmlspecialchars($reserva_checkin['id'], ENT_QUOTES) . "'

                                       data-hospede-checkin-nome='" . htmlspecialchars($reserva_checkin['hospede_nome'], ENT_QUOTES) . "'

                                       data-checkin-entrada='" . htmlspecialchars(formatarDataHora($reserva_checkin['dataentrada'], $reserva_checkin['horaentrada']), ENT_QUOTES) . "'

                                       data-checkin-saida='" . htmlspecialchars(formatarDataHora($reserva_checkin['datasaida'], $reserva_checkin['horasaida']), ENT_QUOTES) . "'";

                            }

                            elseif ($reserva_data) {

                                echo " data-reserva-id='" . htmlspecialchars($reserva_data['id'], ENT_QUOTES) . "'

                                       data-hospede-nome='" . htmlspecialchars($reserva_data['hospede_nome'], ENT_QUOTES) . "'

                                       data-entrada='" . htmlspecialchars(formatarDataHora($reserva_data['dataentrada'], $reserva_data['horaentrada']), ENT_QUOTES) . "'

                                       data-saida='" . htmlspecialchars(formatarDataHora($reserva_data['datasaida'], $reserva_data['horasaida']), ENT_QUOTES) . "'";

                            }

                            

                            echo ">";

                            echo $icones;

                            echo '</td>';

                        endfor;

                        

                        echo "</tr>";

                    endforeach;

                    ?>

                </tbody>

            </table>

        </div>

        

        <!-- Botão Home abaixo do mapa -->

        <div class="text-center mt-4">

            <button onclick="location.href='index.php';" class="btn btn-success">Home</button>

        </div>

    </div>



    <!-- Modal de Detalhes da Reserva -->

    <div class="modal fade" id="reservaModal" tabindex="-1">

        <div class="modal-dialog">

            <div class="modal-content">

                <div class="modal-header">

                    <h5 class="modal-title">Detalhes da Unidade Habitacional</h5>

                    <div class="navigation-buttons">

                        <button type="button" class="btn btn-outline-secondary btn-sm" id="btnAnterior" title="Data anterior">

                            <i class="bi bi-arrow-left"></i>

                        </button>

                        <button type="button" class="btn btn-outline-secondary btn-sm" id="btnProximo" title="Próxima data">

                            <i class="bi bi-arrow-right"></i>

                        </button>

                    </div>

                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>

                </div>

                <div class="modal-body">

                    <p><strong>UH:</strong> <span id="modalUh"></span></p>

                    <p><strong>Data:</strong> <span id="modalData"></span></p>



                    <!-- Lista de reservas do dia -->

                    <div id="reservasLista" style="display: none;" class="mb-3">

                        <h6>Reservas do Dia</h6>

                        <div id="reservasContainer">

                            <!-- Reservas serão criadas dinamicamente via JavaScript -->

                        </div>

                    </div>



                    <!-- Informações da reserva existente (caso normal - não duplo) -->

                    <div id="reservaInfo" style="display: none;">

                        <hr>

                        <h6>Informações da Reserva</h6>

                        <p><strong>Hóspede:</strong> <span id="modalHospede"></span></p>

                        <p><strong>Entrada:</strong> <span id="modalEntrada"></span></p>

                        <p><strong>Saída:</strong> <span id="modalSaida"></span></p>

                    </div>

                </div>

                <div class="modal-footer">

                    <!-- Botões dinâmicos baseados no status -->

                    <a href="#" id="novaReservaLink" class="btn btn-success" style="display: none;">Nova Reserva</a>

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>

                </div>

            </div>

        </div>

    </div>



    <!-- Modal de Seleção de Hóspede -->

    <div class="modal fade" id="selecionarHospedeModal" tabindex="-1">

        <div class="modal-dialog modal-lg">

            <div class="modal-content">

                <div class="modal-header">

                    <h5 class="modal-title">Selecionar Hóspede para Nova Reserva</h5>

                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>

                </div>

                <div class="modal-body">

                    <div class="row mb-3">

                        <div class="col">

                            <label for="buscarHospede" class="form-label">Buscar hóspede existente:</label>

                            <div class="input-group">

                                <input type="text" class="form-control" id="buscarHospede" placeholder="Digite o nome do hóspede...">

                                <button class="btn btn-outline-secondary" type="button" id="btnBuscarHospede">

                                    <i class="bi bi-search"></i>

                                </button>

                            </div>

                            <div id="resultadosBusca" class="mt-2"></div>

                        </div>

                    </div>

                    

                    <div class="text-center my-3">

                        <span class="text-muted">- ou -</span>

                    </div>

                    

                    <div class="d-grid">

                        <button class="btn btn-outline-primary" id="btnNovoHospede">

                            <i class="bi bi-person-plus"></i> Cadastrar Novo Hóspede

                        </button>

                    </div>

                    

                    <input type="hidden" id="uhSelecionada">

                    <input type="hidden" id="dataSelecionada">

                </div>

                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>

                </div>

            </div>

        </div>

    </div>



    <!-- Modal de Cadastro Completo de Hóspede -->

    <div class="modal fade" id="cadastroCompletoHospedeModal" tabindex="-1">

        <div class="modal-dialog modal-lg">

            <div class="modal-content">

                <div class="modal-header">

                    <h5 class="modal-title">Cadastro Completo de Hóspede</h5>

                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>

                </div>

                <div class="modal-body" id="formularioCompletoContainer">

                    <div class="text-center">

                        <div class="spinner-border" role="status">

                            <span class="visually-hidden">Carregando...</span>

                        </div>

                        <p>Carregando formulário...</p>

                    </div>

                </div>

                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>

                </div>

            </div>

        </div>

    </div>



    <!-- Modal de Nova Reserva Direta -->

    <div class="modal fade" id="novaReservaModal" tabindex="-1">

        <div class="modal-dialog modal-lg">

            <div class="modal-content">

                <div class="modal-header">

                    <h5 class="modal-title">Nova Reserva</h5>

                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>

                </div>

                <div class="modal-body">

                    <div id="formularioReservaContainer">

                        <div class="text-center">

                            <div class="spinner-border" role="status">

                                <span class="visually-hidden">Carregando...</span>

                            </div>

                            <p>Carregando formulário...</p>

                        </div>

                    </div>

                </div>

                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>

                </div>

            </div>

        </div>

    </div>



    <!-- Modal de Editar Reserva -->

    <div class="modal fade" id="editarReservaModal" tabindex="-1">

        <div class="modal-dialog modal-lg">

            <div class="modal-content">

                <div class="modal-header">

                    <h5 class="modal-title">Editar Reserva</h5>

                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>

                </div>

                <div class="modal-body" id="formularioEditarContainer">

                    <div class="text-center">

                        <div class="spinner-border" role="status">

                            <span class="visually-hidden">Carregando...</span>

                        </div>

                        <p>Carregando formulário...</p>

                    </div>

                </div>

                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>

                </div>

            </div>

        </div>

    </div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Scripts modularizados do Mapa UH -->

    <script type="module" src="custom/js/func.js"></script>

    <script type="module" src="custom/js/valida_data_anterior.js"></script>

    <script type="module" src="custom/js/modal-detalhes.js"></script>

    <script type="module" src="custom/js/modal-hospede.js"></script>

    <script type="module" src="custom/js/verificacao-disponibilidade-local.js"></script>

    <script type="module" src="custom/js/modal-reserva.js"></script>

    <script type="module" src="custom/js/modal-editar-reserva.js"></script>

    <script type="module" src="custom/js/mapa-uh-main.js"></script>



</body>

</html>

