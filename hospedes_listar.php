<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="custom/css/form_fnrh.css">
    <link rel="stylesheet" href="custom/css/valida_data_anterior.css">
    <link rel="stylesheet" href="custom/css/responsive-fixes.css">
    <title>Listagem de Hospedes</title>
</head>
<body> 
<center>
<h1>Lista de Hospedes</h1>
<br>

<!-- Botões para filtrar hospedes -->
<div class="mb-3">
    <a href="?page=listar&filtro=todos" class="btn btn-outline-secondary filtro-btn">Todos</a>
    <a href="?page=listar&filtro=atuais" class="btn btn-outline-success filtro-btn">Atuais</a>
    <a href="?page=listar&filtro=futuros" class="btn btn-outline-primary filtro-btn">Futuros</a>
    <a href="?page=listar&filtro=anteriores" class="btn btn-outline-warning filtro-btn">Anteriores</a>
</div>

<form method="POST" class="mb-4">
    <div class="row justify-content-center align-items-center">
        <div class="col-auto">
            <label for="order" class="col-form-label">Ordenar por:</label>
        </div>
        <div class="col-auto">
            <select name="order" id="order" class="form-control">
                <option value="id">Ordem de Entrada</option>
                <option value="nome">Nome</option>
                <option value="cidade,nome">Cidade</option>		
                <option value="uf,cidade,nome">Estado</option>
            </select>
        </div>
        <div class="col-auto">
            <button type="submit" class="btn btn-primary">Ordenar</button>
        </div>
        <div class="col-auto">
            <button type="button" onclick="abrirModalNovoHospede()" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> Novo Hóspede
            </button>
            <button type="button" onclick="location.href='index.php';" class="btn btn-success">Home</button>
        </div>
    </div>
</form>

</center>

<?php
// config.php já foi incluído pelo index.php

function renderTable($res) {
    global $conn;
    
    echo "<table class='table table-hover table-striped table-bordered table-responsive-stack'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>Status</th>"; // Coluna para o ícone de status
    echo "<th>Nome</th>";
    echo "<th>CPF</th>";
    echo "<th>Cidade</th>";
    echo "<th>UF</th>";
    echo "<th>Ações</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";

    while ($row = $res->fetch_object()) {
        $hospede_id = $row->id;
        $hoje = date('Y-m-d');
        
        // Verificar hospedes atuais (com dataentrada <= hoje e datasaida >= hoje)
        $sql_atuais = "SELECT COUNT(*) as total FROM reservas 
                      WHERE hospede_id = $hospede_id 
                      AND dataentrada <= '$hoje' 
                      AND datasaida >= '$hoje'";
        
        $res_atuais = $conn->query($sql_atuais);
        $atuais_count = 0;
        
        if ($res_atuais && $res_atuais->num_rows > 0) {
            $atuais_data = $res_atuais->fetch_object();
            $atuais_count = $atuais_data->total;
        }
        
        // Verificar hospedes com reservas futuras (com dataentrada > hoje)
        $sql_futuros = "SELECT COUNT(*) as total FROM reservas 
                       WHERE hospede_id = $hospede_id 
                       AND dataentrada > '$hoje'";
        
        $res_futuros = $conn->query($sql_futuros);
        $futuros_count = 0;
        
        if ($res_futuros && $res_futuros->num_rows > 0) {
            $futuros_data = $res_futuros->fetch_object();
            $futuros_count = $futuros_data->total;
        }
        
        // Verificar hospedes com reservas passadas
        $sql_passadas = "SELECT COUNT(*) as total FROM reservas 
                        WHERE hospede_id = $hospede_id 
                        AND datasaida < '$hoje'";
        
        $res_passadas = $conn->query($sql_passadas);
        $passadas_count = 0;
        
        if ($res_passadas && $res_passadas->num_rows > 0) {
            $passadas_data = $res_passadas->fetch_object();
            $passadas_count = $passadas_data->total;
        }
        
        // Definir o ícone e texto de status
        if ($atuais_count > 0) {
            // Hospede atual - ícone verde
            $status_icon = '<i class="bi bi-person-check-fill status-icon atual" title="Hospedado atualmente"></i>';
        } elseif ($futuros_count > 0) {
            // Hospede com reserva futura - ícone azul
            $status_icon = '<i class="bi bi-calendar-check status-icon futuro" title="Com reserva futura"></i>';
        } elseif ($passadas_count > 0) {
            // Hospede com reservas passadas - ícone amarelo
            $status_icon = '<i class="bi bi-calendar-x status-icon anterior" title="Apenas hospedagens passadas"></i>';
        } else {
            // Sem reservas - ícone cinza
            $status_icon = '<i class="bi bi-person-dash status-icon sem-reserva" title="Sem reservas"></i>';
        }
        
        echo "<tr>";
        echo "<td data-label='Status' class='text-center'>" . $status_icon . "</td>";
        echo "<td data-label='Nome'>".$row->nome."</td>";
        echo "<td data-label='CPF'>".$row->cpf."</td>";
        echo "<td data-label='Cidade'>".$row->cidade."</td>";
        echo "<td data-label='Estado'>".$row->uf."</td>";
        echo "<td data-label='Ações' class='text-center'>
               <button type='button' onclick='event.preventDefault(); abrirModalEdicaoHospede(" . $row->id . "); return false;' class='btn btn-sm btn-primary action-icon' title='Editar'><i class='bi bi-pencil-square'></i></button>
               <a href='index.php?page=reservas&hospede_id=" . $row->id . "&hospede_nome=" . addslashes($row->nome) . "' class='btn btn-sm btn-info action-icon' title='Reservas'><i class='bi bi-calendar-week'></i></a>
               <a href='javascript:void(0)' onclick=\"if(confirm('Tem certeza que deseja excluir?')){location.href='index.php?page=salvar&acao=excluir&hospede_id=".$row->id."&hospede_nome=" . addslashes($row->nome) . "';}\" class='btn btn-sm btn-danger action-icon' title='Excluir'><i class='bi bi-trash'></i></a>
              </td>";
        echo "</tr>";
    }

    echo "</tbody>";
    echo "</table>";
}

$type = $_POST['tipo_pesquisa'] ?? $_GET['tipo_pesquisa'] ?? '';
$term = $_POST['termo_pesquisa'] ?? $_GET['termo_pesquisa'] ?? '';
$order = $_POST['order'] ?? 'id'; // Default order
$filtro = $_GET['filtro'] ?? 'todos'; // Novo parâmetro para filtrar hospedes

$where = " WHERE pousada_id = '$pousada_id'";
if (!empty($type) && !empty($term)) {
    $where = "WHERE `$type` LIKE '%$term%' AND pousada_id = '$pousada_id'";
}

// SQL base para selecionar hospedes
$sql = "SELECT h.* FROM hospedes h";

// Aplicar filtros específicos
if ($filtro == 'atuais') {
    $hoje = date('Y-m-d');
    $sql = "SELECT DISTINCT h.* FROM hospedes h 
            INNER JOIN reservas r ON h.id = r.hospede_id 
            WHERE h.pousada_id = '$pousada_id' 
            AND r.dataentrada <= '$hoje' 
            AND r.datasaida >= '$hoje'";
    
    if (!empty($type) && !empty($term)) {
        $sql .= " AND h.`$type` LIKE '%$term%'";
    }
} elseif ($filtro == 'futuros') {
    $hoje = date('Y-m-d');
    $sql = "SELECT DISTINCT h.* FROM hospedes h 
            INNER JOIN reservas r ON h.id = r.hospede_id 
            WHERE h.pousada_id = '$pousada_id' 
            AND r.dataentrada > '$hoje'";
    
    if (!empty($type) && !empty($term)) {
        $sql .= " AND h.`$type` LIKE '%$term%'";
    }
} elseif ($filtro == 'anteriores') {
    $hoje = date('Y-m-d');
    $sql = "SELECT DISTINCT h.* FROM hospedes h 
            INNER JOIN reservas r ON h.id = r.hospede_id 
            WHERE h.pousada_id = '$pousada_id' 
            AND r.datasaida < '$hoje'
            AND NOT EXISTS (
                SELECT 1 FROM reservas r2 
                WHERE r2.hospede_id = h.id 
                AND (r2.dataentrada <= '$hoje' AND r2.datasaida >= '$hoje' OR r2.dataentrada > '$hoje')
            )";
    
    if (!empty($type) && !empty($term)) {
        $sql .= " AND h.`$type` LIKE '%$term%'";
    }
} else {
    // Filtro 'todos' - usar a consulta padrão
    $sql .= " $where";
}

$sql .= " ORDER BY $order";

$res = $conn->query($sql);
$qtd = $res->num_rows;

if ($qtd > 0) {
    renderTable($res);
} else {
    echo "<p class='alert alert-danger'>Não existem Hospedes cadastrados com os filtros selecionados.</p>";
}
print "<center>";
print "<button onclick=\"location.href='index.php';\" class='btn btn-success'>Home</button>";
print "</center>";
?>

<!-- Modal de Editar Hóspede -->
<div class="modal fade" id="editarHospedeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Editar Hóspede</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="formularioEditarHospedeContainer">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p>Carregando formulário...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Cadastro Completo de Hóspede -->
<div class="modal fade" id="cadastroCompletoHospedeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cadastro Completo de Hóspede</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="formularioCompletoContainer">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p>Carregando formulário...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Scripts dos modais -->
<script src="custom/js/modal-editar-hospede.js"></script>
<script type="module" src="custom/js/modal-hospede.js"></script>
<script>
console.log('Scripts carregados. ModalEditarHospede disponível:', typeof window.ModalEditarHospede);
</script>

<script>
let modalEditarHospede;
let modalHospede;

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar modais após carregamento da página
    if (window.ModalEditarHospede) {
        modalEditarHospede = new window.ModalEditarHospede();
        console.log('Modal de editar hóspede inicializado');
    } else {
        console.error('ModalEditarHospede não encontrado');
    }

    if (window.ModalHospede) {
        modalHospede = new window.ModalHospede();
        console.log('Modal de novo hóspede inicializado');
    } else {
        console.error('ModalHospede não encontrado');
    }
});

function abrirModalEdicaoHospede(hospedeId) {
    console.log('Tentando abrir modal para hóspede ID:', hospedeId);

    if (modalEditarHospede) {
        modalEditarHospede.carregarFormularioEdicao(hospedeId);
    } else {
        console.error('Modal não inicializado, tentando inicializar...');
        // Fallback: tentar inicializar novamente
        if (window.ModalEditarHospede) {
            modalEditarHospede = new window.ModalEditarHospede();
            modalEditarHospede.carregarFormularioEdicao(hospedeId);
        } else {
            // Fallback final: mostrar alerta de erro
            console.warn('Modal não disponível');
            alert('Erro: Modal de edição não está disponível. Recarregue a página e tente novamente.');
        }
    }
}

function abrirModalNovoHospede() {
    console.log('Tentando abrir modal de novo hóspede');

    if (modalHospede) {
        modalHospede.carregarFormularioCompleto();
    } else {
        console.error('Modal não inicializado, tentando inicializar...');
        // Fallback: tentar inicializar novamente
        if (window.ModalHospede) {
            modalHospede = new window.ModalHospede();
            modalHospede.carregarFormularioCompleto();
        } else {
            // Fallback final: mostrar alerta de erro
            console.warn('Modal não disponível');
            alert('Erro: Modal de novo hóspede não está disponível. Recarregue a página e tente novamente.');
        }
    }
}
</script>

</body>
</html>
