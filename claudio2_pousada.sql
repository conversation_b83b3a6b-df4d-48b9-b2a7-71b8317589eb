-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Tempo de geração: 15/08/2025 às 19:14
-- V<PERSON><PERSON> do servidor: 8.0.42-cll-lve
-- Versão do PHP: 8.4.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON><PERSON> de dados: `claudio2_pousada`
--

-- --------------------------------------------------------

--
-- Estrutura para tabela `caixa_diario`
--

CREATE TABLE `caixa_diario` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `data_abertura` datetime NOT NULL,
  `data_fechamento` datetime DEFAULT NULL,
  `saldo_inicial` decimal(10,2) NOT NULL DEFAULT '0.00',
  `saldo_final` decimal(10,2) DEFAULT NULL,
  `usuario_abertura_id` int NOT NULL,
  `usuario_fechamento_id` int DEFAULT NULL,
  `status` enum('aberto','fechado') NOT NULL DEFAULT 'aberto',
  `observacao` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `caixa_diario`
--

INSERT INTO `caixa_diario` (`id`, `pousada_id`, `data_abertura`, `data_fechamento`, `saldo_inicial`, `saldo_final`, `usuario_abertura_id`, `usuario_fechamento_id`, `status`, `observacao`) VALUES
(3, 1, '2025-07-11 17:13:52', NULL, 0.00, NULL, 31, NULL, 'aberto', '');

-- --------------------------------------------------------

--
-- Estrutura para tabela `categorias_financeiras`
--

CREATE TABLE `categorias_financeiras` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `nome` varchar(100) NOT NULL,
  `tipo` enum('receita','despesa') NOT NULL,
  `descricao` text,
  `cor` varchar(7) DEFAULT '#000000',
  `is_default` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `categorias_financeiras`
--

INSERT INTO `categorias_financeiras` (`id`, `pousada_id`, `nome`, `tipo`, `descricao`, `cor`, `is_default`) VALUES
(19, 1, 'Hospedagem', 'receita', NULL, '#28a745', 1),
(20, 1, 'Manutenção', 'despesa', 'Manutenções diversas', '#fff70a', 0),
(21, 1, 'Alimentação', 'despesa', '', '#e60abe', 0),
(22, 1, 'Amenites e insumos', 'despesa', '', '#20c4d9', 0),
(23, 1, 'Contas', 'despesa', '', '#5e13cd', 0);

-- --------------------------------------------------------

--
-- Estrutura para tabela `contratos`
--

CREATE TABLE `contratos` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `plano_id` int NOT NULL,
  `data_contratacao` date NOT NULL,
  `data_expiracao` date DEFAULT NULL,
  `status` enum('ativo','inativo') DEFAULT 'ativo'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `contratos`
--

INSERT INTO `contratos` (`id`, `pousada_id`, `plano_id`, `data_contratacao`, `data_expiracao`, `status`) VALUES
(1, 1, 1, '2024-08-01', NULL, 'ativo'),
(2, 2, 1, '2024-09-01', NULL, 'ativo');

-- --------------------------------------------------------

--
-- Estrutura para tabela `formas_pagamento`
--

CREATE TABLE `formas_pagamento` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text,
  `is_active` tinyint(1) DEFAULT '1',
  `afeta_caixa` tinyint(1) DEFAULT '0' COMMENT 'Indica se esta forma de pagamento afeta o caixa físico (1=sim, 0=não)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `formas_pagamento`
--

INSERT INTO `formas_pagamento` (`id`, `pousada_id`, `nome`, `descricao`, `is_active`, `afeta_caixa`) VALUES
(6, 1, 'PIX', '', 0, 0),
(7, 1, 'CRÉDITO NA TON', '', 1, 0),
(8, 1, 'DÉBITO NA TON', '', 1, 0),
(9, 1, 'PIX CONTA BRADESCO', '', 1, 0),
(10, 1, 'TON', '', 0, 0),
(11, 1, 'PIX NA CONTA TON', '', 1, 0),
(12, 1, 'EM ESPÉCIE', '', 1, 0);

-- --------------------------------------------------------

--
-- Estrutura para tabela `hospedes`
--

CREATE TABLE `hospedes` (
  `id` int NOT NULL,
  `nome` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `nasc` date DEFAULT NULL,
  `idade` tinyint DEFAULT NULL,
  `profissao` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nacionalidade` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sexo` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cpf` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `documento` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tipo` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `expedidor` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `endereco` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `telefone` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cep` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cidade` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `uf` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pais` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pousada_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `hospedes`
--

INSERT INTO `hospedes` (`id`, `nome`, `nasc`, `idade`, `profissao`, `nacionalidade`, `sexo`, `cpf`, `documento`, `tipo`, `expedidor`, `endereco`, `telefone`, `cep`, `cidade`, `uf`, `pais`, `email`, `pousada_id`) VALUES
(1, 'Wandemária Rodrigues da Silva', '1986-12-29', 38, 'Cozinheira', 'brasileira', 'feminino', '01295566370', '0000000000', 'rg', 'SSP/PI', 'Rua Acelino Coelho Resende/Centro', '(86) 9 9908-2698', '64500-000', 'Capitão De  Campos', 'PI', 'Brasil', '<EMAIL>', 1),
(2, 'Núbia Nascimento de Oliveira', '1987-04-14', 37, 'Advogada', 'brasileira', 'feminino', '01897550324', '0000000000', 'rg', 'SSP/PI', 'Povoado Alto Bonito/ Zona Rural', '(86) 9 9430-0460', '64500-000', 'Capitão De  Campos', 'PI', 'Brasil', '<EMAIL>', 1),
(3, 'Marcos Antônio Silva Nascimento', '1988-04-28', 36, 'Agente de Microcrédito', 'brasileiro', 'masculino', '02758099357', '0000000000', 'rg', 'SSP/PI', 'Rua Henrique de Freitas 1225/ Prado', '(86) 9 9940-9159', '64500-000', 'Piripiri', 'PI', 'Brasil', '<EMAIL>', 1),
(4, 'Francisca Fernanda Alves da Silva', '1984-05-10', 40, 'auxiliar escolar', 'brasileira', 'feminino', '36326535859', '0000000000', 'rg', 'SSP/PI', 'Rua São João 172/ Bairro Vila da  Paz', '(86) 9 9548-6594', '64500-000', 'Capitão De  Campos', 'PI', 'Brasil', '<EMAIL>', 1),
(5, 'Taiane Soares Pinto', '1991-04-05', 33, 'Funcionária pública   ', 'brasileira', 'feminino', '04148935307', '00000000', 'rg', 'SSP/PI', 'Rua Cosmo Barros n ° 05/ Conjunto Chico Café', '(86) 9 9555-3292', '64500-000', 'Pedro II', 'PI', 'Brasil', '<EMAIL>', 1),
(6, 'Jakson Cavalcante De Mesquita', '1982-08-27', 41, 'mecânico', 'brasileiro', 'masculino', '00003465357', '00000', 'rg', 'SSP/PI', 'Rua  Cícero Medeiros Barbosa/245/ Prado', '(86) 9 9803-0627', '64500-000', 'Piripiri', 'PI', 'Brasil', ' <EMAIL>', 1),
(7, 'Antônio André de Sousa', '1987-04-27', 37, 'Técnico Agente de Saúde', 'brasileiro', 'masculino', '02057176320', '0000000000', 'rg', 'SSP/PI', 'Rua projetada 32, sem número, bairro Estação', '(86) 9 9442-4969', '64500-000', 'Capitão De  Campos', 'PI', 'Brasil', '<EMAIL>', 1),
(8, 'Marcelo Rocha Nascimento', '0000-00-00', 0, '', 'brasileiro', 'masculino', '64081044368', '', '', 'SSP/PI', 'Loteamento Morada dos Ventos/ Quadra 21 ', '(86) 9850-4380', '64500-000', 'Parnaíba', 'PI', 'Brasil', '', 1),
(9, 'Felipe Douglas Magalhães', '0000-00-00', 0, 'Coordenador Cultura Pedra', 'brasileiro', 'masculino', NULL, '00000', 'rg', 'SSSP/CE', 'Pedra Branca - Ceará', '(88) 9 9604-5043', '63630-000', 'Pedra Branca', 'CE', 'Brasil', '', 1),
(10, 'Anderson Soares de Oliveira', '0000-00-00', 0, 'Artista Popular', 'brasileiro', 'masculino', NULL, '', '', 'SSSP/CE', 'Fortaleza-Ce', '(88) 9 8872-0562', '63310-000', 'Umari', 'CE', 'Brasil', '', 1),
(11, 'Maria Carneiro (Mariazinha)', '0000-00-00', 0, 'Funcionária SESC', 'brasileira', 'feminino', '22635068372', '', '', 'SSSP/CE', 'IBIAPINA-CE', '(88) 909776-7780', '62360-000', 'Ibiapina', 'CE', 'Brasil', '', 1),
(12, 'Derlane Vieira dos Santos', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', 'SSSP/CE', 'pEDRA bRANCA-cE', '(88) 9 9652-9890', '63630-000', 'Pedra Branca', 'CE', 'Brasil', '', 1),
(13, 'Victor Alan Rodrigues Nunes', '0000-00-00', 0, 'Artesão', 'brasileiro', 'masculino', '08235510324', '', '', 'SSSP/CE', '', '(88) 9 9729-8686', '63475-000', 'Jaguaribe', 'CE', 'Brasil', '', 1),
(14, 'Francisco Luis Texeira Santos', '0000-00-00', 0, 'Coordenador Cultura Umari', 'brasileiro', 'masculino', NULL, '', '', 'SSSP/CE', 'FALTA', '(88) 9 8825-1517', '63310-000', 'Umari', 'CE', 'Brasil', '', 1),
(15, 'Cristiano Costa de Souza', '0000-00-00', 0, 'Motorista SESC', 'brasileiro', 'masculino', NULL, '', '', 'SSSP/CE', 'Fortaleza-Ce', '(88) 9 8713-2174', '60000-000', 'Fortaleza', 'CE', 'Brasil', '', 1),
(16, 'Isaac Alves Gomes', '1988-05-24', 36, 'Trabalho autônomo', 'brasileiro', 'masculino', '04869439336', '0000000000', 'rg', 'SSSP/CE', 'Rua Padre Tomé Carvalho', '(88) 9447-0897', '62380-000', 'Guaraciaba do norte', 'CE', 'Brasil', '<EMAIL>', 1),
(17, 'Danielle Costa', '0000-00-00', 0, 'Professor', 'brasileira', 'feminino', NULL, '', '', 'SSP/PI', 'Rua Agnelo Pereira da Silva, 3477', '(61) 98263-2115', '64045-440', 'Teresina', 'PI', 'Brasil', '', 1),
(18, 'Macedrânia Maria Moreira de Moraes ', '1981-09-05', 42, 'Administrador', 'brasileira', 'feminino', '88064972353', '0000000000', 'rg', 'SSSP/CE', 'Rua Paulo Costa, 148, Arapixi, São Gonçalo do Amar', '(85) 9 9153-7954', '62670-000', 'São Gonçalo do Amarante', 'CE', 'Brasil', '<EMAIL>', 1),
(19, 'Ray Martins e Flávia Lima', '1995-10-26', 28, 'chef restaurante', 'brasileiro', 'masculino', '06700253306', '0000000000', 'rg', 'SSSP/CE', 'Praia do Preá  Cruz- CE rua leopoldo martins s/n', '(88) 9 8878-6953', '62595000', 'Cruz', 'CE', 'Brasil', '<EMAIL>', 1),
(20, 'Flavio Pereira Soares', '0000-00-00', 0, 'Advogado', 'brasileiro', 'masculino', NULL, '', '', 'SSP/PI', '', '(86) 9 9967-9565', '64500-000', 'Teresina', 'PI', 'Brasil', '', 1),
(21, 'Vanderys Rocha Cardoso', '0000-00-00', 0, 'Autônomo', 'brasileiro', 'masculino', NULL, '', '', 'SSSP/CE', 'Travessa Manoel Mergulhão, 23/Centro', '85 9 8791-9803', '62300-000', 'Maracanaú', 'CE', 'Brasil', '', 1),
(22, 'Edson Cassini Deljino', '0000-00-00', 0, 'Vendedores Autônomos', 'brasileiro', 'masculino', NULL, '', 'rg', '0', 'Rua Ana Almeida de Castro 124', '83 9 8610-6181', '', 'Campina Grande ', 'PB', 'Brasil', '', 1),
(23, 'Marinho (Produtor)-Forró Briseira', '0000-00-00', 0, 'Produtor', 'brasileiro', 'masculino', NULL, '', '', 'SSSP/CE', 'Fortaleza-Ce', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(24, 'Netinho Nogueira e Val Mendes (F.Briseira)', '0000-00-00', 0, 'Artistas', 'brasileiro', 'masculino', NULL, '', '', '0', 'Fortaleza-Ce', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(25, 'Denize Antunes Nobre', '0000-00-00', 0, 'Comerciante', 'brasileiro', 'masculino', NULL, '', '', 'SSSP/CE', 'Fortaleza-Ce', '(85)9 8778-1988', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(26, 'Lucas Mendes Magalhães', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'AV.Principal Jardim América ,001/Centro', '', '', 'São Luis', 'MA', 'Brasil', '', 1),
(27, 'Juruviaran e Igor Ribeiro-Forró Briseira', '0000-00-00', 0, 'Artistas', 'brasileiro', 'masculino', NULL, '', '', '', 'Fortaleza-Ce', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(28, 'Felipe Mota (Forró Briseira)', '0000-00-00', 0, 'Artistas', 'brasileiro', 'masculino', NULL, '', '', 'SSSP/CE', 'Fortaleza-Ce', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(29, 'Lucas Bianco Araújo', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Das Margaridas, /Paraiso das rosas', '98 9 8603-5549', '0', 'São José De Ribamar', 'MA', 'Brasil', '', 1),
(30, 'Izolete (Amiga irmão Dourival)', '0000-00-00', 0, 'Autônoma', 'brasileira', 'feminino', NULL, '', '', '', 'Campo - Maior-Pi', '', '', 'Campo-Maior', 'PI', 'Brasil', '', 1),
(31, 'Dourival Ferreira Brandão', '0000-00-00', 0, 'Representante', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua E Q-04 C-21/Resid. Lila/Fátima', '(86)9 8124-1777', '', 'Campo-Maior', 'PI', 'Brasil', '', 1),
(32, 'Cezar Campêlo Filho', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Bairro Marquês', '', '', 'Teresina', 'PI', 'Brasil', '', 1),
(33, 'José Alexandre E. Pedrosa', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Nova Russas', '88 9 9331-7831', '', 'Nova Russas', 'CE', 'Brasil', '', 1),
(34, 'Silvani Braga  de Queiroz (Irmãos)', '0000-00-00', 0, 'Comerciante', 'brasileira', 'feminino', NULL, '', '', '', 'Sítio Caracol', '', '', 'Tianguá', 'CE', 'Brasil', '', 1),
(35, 'Lucas Messias Araújo ', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Francisco das C. Barreto Lima', '(88) 9 8158-1431', '', 'Sobral', 'CE', 'Brasil', '', 1),
(36, 'Amanda da Silva Rodrigues(Amandinha)', '0000-00-00', 0, 'Cantores/Amandinha', 'brasileira', 'feminino', NULL, '', '', '', 'Fortaleza-Ce', '(88) 9 9725-1741', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(37, 'Francisco de Assis Sales Carvalho', '0000-00-00', 0, 'Músicos/Banda Carlinhos Patriolino ', 'brasileiro', 'masculino', NULL, '', '', '', 'DE FORTALEZA', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(38, 'Júlio César Isidoro da Silva (MARINALDO BANDOLIM)', '0000-00-00', 0, 'Artistas/ Sebrae', 'brasileiro', 'masculino', NULL, '', '', '', 'Fortaleza-Ce', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(39, 'Elison Carvalho ', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'CJ. Raul Bacelar 4 Etapa / Bairro Planalto', '86 9 9942-4846', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(40, 'Maria Lúcia de Souza Fauth', '0000-00-00', 0, 'Autônoma', 'brasileira', 'feminino', NULL, '', '', '', 'Rua Pinheiro Maia, 180/ Cidade dos func.', '(88) 9 8181-8212', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(41, 'Maria Lúcia Lustosa Farias', '0000-00-00', 0, 'Autônoma', 'brasileira', 'feminino', NULL, '', '', '', 'Rua J. da Penha 332 ap 701/ centro/Fortaleza-Ce', '(85) 9 8848-5321', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(42, 'Sandoval Barroso do Nascimento', '1968-08-02', 56, 'Consultor Educação', 'BRASILEIRO', 'MASCULINO', '34821775387', '', '', '', 'Rua Padre Domingos, 1666 Centro', '(86) 9 9905-0185', '64.260-000', 'Piripiri', 'PI', 'Brasil', '', 1),
(43, 'Luciandro de Castro Leitão', '1975-07-03', 49, 'GERENTE DE LOGÍSTICA', 'Brasileiro', 'Masculino', '69680205304', '', '', '0', 'Rua Murara 101 Bloco 02  Apto. 402/ Messejana', '(85) 9 9946-2986', '60.872-690', 'Fortaleza', 'CE', 'Brasil', '', 1),
(44, 'Ricardo Rodrigues da Silva Nascimento', '0000-00-00', 0, 'Professor', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Argentina, Casa 07 Q-18 RODOVIÁRIA', '', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(45, 'Amanda Dos Santos Ferreira', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Rua Luís Soares Do Carmo, 55', '(85) 9 9177-4406', '', 'Pentecoste', 'CE', 'Brasil', '', 1),
(46, 'Àlvaro Maurício Duarte Freitas', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'R. Maria Iara Gomes 244/Paracuru Beach', '(85) 9 94317081', '440 reaia 2 diárias', 'PARAIPABA-CE', 'CE', 'Brasil', '', 1),
(47, 'Inácio Carvalho Rocha', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'AV.Professor Irineu Linhares Lima/149 dr. Joel Mar', '(85) 9 8428-8610', '', 'Nova Russas', 'CE', 'Brasil', '', 1),
(48, 'Ana Thayná Rodrigues', '0000-00-00', 0, 'Massoterapeuta', 'brasileira', 'feminino', NULL, '', '', '', 'Rua Frei Alvaro, 541/ alto do cristo', '(88) 9 9407-8259', '500/3 diarias', 'Sobral', 'CE', 'Brasil', '', 1),
(49, 'Francisco Pereira Lima ', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'R. Reverendo Osmar Lima/1959', '(85) 9 8795-0591', '250/1 diária', 'Fortaleza', 'CE', 'Brasil', '', 1),
(50, 'Edilbener Alves da Silva', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'R. João Quinco 263- Centro', '(88) 9 9790-4279', '300/1 diária', 'Alto Santo', 'CE', 'Brasil', '', 1),
(51, 'Cleandro Sandys N. de Sousa ', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'R. Timbira Jardim 244 Jardim Esperança II', '(21) 9 7973-8008', '0', 'Parnaíba', 'PI', 'Brasil', '', 1),
(52, 'Charly Helton', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Dr. Paulo Matos- 175', '(88) 9 9961-4970', '', 'Nova Russas', 'CE', 'Brasil', '', 1),
(53, 'Raimundo Nonato de Moraes ', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua 03 n19/ Altos do Calhau', '(98) 9 8116-0936', '680/ 4 diárias ', 'São Luis', 'PI', 'Brasil', '', 1),
(54, 'Francisco Aurélio Bertoldo ', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Modesto Mendonça 346/ Centro', '(88) 9 9615-5741', '180/1 diária', 'Varjota', 'CE', 'Brasil', '', 1),
(55, 'Ana Regina Ribeiro ', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'R. Francisco Bezerra/ 835- Centro', '(88) 9 9804-7114', '180/1 diária', 'Guaraciaba do norte', 'CE', 'Brasil', '', 1),
(56, 'José Wladimir Rodrigues Da Silva', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Nelson Taváres 69-Pedrinhas', '(88) 9 9461- 4086', '350 reais 2 diárias', 'Sobral', 'CE', 'Brasil', '', 1),
(57, 'Kayo Emanuel Teles C. Morães', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Helvídio Ferraz 315/ Jóquey', '(88) 9 9935-6368', '300 reais 3 diárias', 'Teresina', 'PI', 'Brasil', '', 1),
(58, 'Francisco de Assis Vieira da Silva  (Carnaval)', '0000-00-00', 0, 'Advogado', 'brasileiro', 'masculino', '21785481304', '', '', '', 'Rua Prof. Darcy Araújo, 298/ Centro', '(86) 9 9937- 7958 ', '', 'Luís Correia', 'PI', 'Brasil', '', 1),
(59, 'Luana Meyre Cordeiro Silva ', '0000-00-00', 0, 'Autônoma', 'brasileira', 'feminino', NULL, '', '', '', 'Rua Nazaré Vasconcelos 1705/ Centro', '(88) 9 9793-3063', '670 reais 4 diarias', 'Jijoca de Jerycoacoara', 'CE', 'Brasil', '', 1),
(60, 'Dra. Dandara Dos Santos ', '0000-00-00', 0, 'Cliníca Geral', 'brasileira', 'feminino', NULL, '', '', '', 'Parníba-Pi', '(86) 9 9445-1303', '270  3 diárias', 'Parnaíba', 'PI', 'Brasil', '', 1),
(61, 'Hilton Martins de Sousa', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'R. José Fernandes/ 400, centro ', '(86) 9 9442-4969', '', 'Capitão de Campos', 'PI', 'Brasil', '', 1),
(62, 'Bruna Ponte Siqueira ( Mel&Cachaça/2024)', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', 'SSSP/CE', 'Rua Napoleão Linhares Madeira', '(88) 9 9725-1741', '', 'Sobral', 'CE', 'Brasil', '', 1),
(63, 'Jose Roberto Fernades Lima ', '1981-05-16', 43, 'Representante Comercial Motopeças B', 'brasileiro', 'masculino', NULL, '00000000', 'rg', '0', 'R. Eduardo de Almeida Sanford 473', '(88) 9 9780- 4312', '6222390', 'Sobral', 'CE', 'Brasil', '<EMAIL>', 1),
(64, 'Maristela Matos de C. da Silva', '0000-00-00', 0, 'Autônoma', 'brasileira', 'feminino', NULL, '', '', '', 'Marechal Pires Ferreira 675/ B.Fátima', '(86) 9 9454-1804', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(65, 'Francisco Oliveira Barbosa', '0000-00-00', 0, 'Técnico em Informática', 'brasileiro', 'masculino', '42197660306', '', '', '', 'R. Conselheiro Araújo Lima 1645 Dom Lustosa', '(85) 9 8748-3024', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(66, 'Maralima de Castro da Silva/Dourival', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Rua E Q-04 C-21/Residencial. Lila/ Bairro Fátima', '(86) 9 8124-1777', '', 'Campo-Maior', 'PI', 'Brasil', '', 1),
(67, 'Brena Virna de Carvalho Passos', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', 'SSP/PI', 'AV. Raul Lopes 1905/ Ed.Santorini AP.103', '', '', 'Teresina', 'PI', 'Brasil', '', 1),
(68, 'George de Castro e Silva (Pastor)', '0000-00-00', 0, 'Pastor', 'brasileiro', 'feminino', NULL, '', '', '', 'Conjunto Betânia', '(86) 9 9406-1969', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(69, 'Gabriel Isaac G. S. Castro', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Conjunto Betânia', '', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(70, 'Adelma Cunha Dantas', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Rua Zizi Pontes n 1416', '(85) 9 9716-5866', '', 'Sobral', 'CE', 'Brasil', '', 1),
(71, 'Roberto do R. Mello Carneiro', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Governador Tibério Nunes/1000 Ilhotas', '(86) 9 8844-9942', '', 'Teresina', 'PI', 'Brasil', '', 1),
(72, 'Juliana Chaves Mota', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'AV. Isabela Moreira Gomes 186/ Boa Vizinhança', '(88) 9 9260-1370', '', 'Sobral', 'CE', 'Brasil', '', 1),
(73, 'Eliziane Bezerra Melo', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Rua Equador n1840/ cidade Nova', '(86) 9 9815-2652', '', 'Teresina', 'PI', 'Brasil', '', 1),
(74, 'Yure Emerson Coutinho de Carvalho', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'R. Ant Irapuan Bonfim 55/ Alto Brilhante ', '(88) 9 9641-6079', '', 'Quiterianópolis ', 'CE', 'Brasil', '', 1),
(75, 'Elisamá de Paula Maia ', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Rua 20 Q-16 C-14/ Resid. labelle Park- Paço do Lum', '(98) 9 9618-0001', '', 'São Luis', 'CE', 'Brasil', '', 1),
(76, 'Kamylla Silva Rodrigues ', '1994-12-28', 29, 'recepcionista', 'brasileira', 'feminino', NULL, '', '', '', 'R. Fco Alfredo Cavalcante, 159 Dr josé Euclides', '+55 88 9434-3753', '', 'Sobral', 'CE', 'Brasil', '', 1),
(77, 'Gilberto Luciano Marques', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'R. José Ourives, 301 centro', '(85) 9 9814-9938', '', 'São Gonçalo do Amarante', 'CE', 'Brasil', '', 1),
(78, 'Uchôa Neto', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'R.Catarina Laboure.Monte Castelo 128/Centro', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(79, 'Régis Medeiros', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'AV.dos nacionalistas 354/Jardim Tango', '(11) 9 8549-6310', '', 'São Paulo', 'SP', 'Brasil', '', 1),
(80, 'Francisco Roseno dos Santos', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'AV. Gonçalves Dias 38/ Parquelândia', '(85) 9 9130-6297', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(81, 'Rômulo Massariol de Sousa', '0000-00-00', 0, 'Esportistas (competidores)', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Araguaia 402/ Bairro da Paz', '(94) 9 9113-8529', '', 'Parauapebas', 'PA', 'Brasil', '', 1),
(82, 'Thairo Santiago (PIOCERÁ)', '0000-00-00', 0, 'COMPETIDORES DE HALLY', 'brasileiro', 'masculino', NULL, '', '', '', 'AV.Visconde do Rio Branco 2250/Centro', '(91) 9 8138-7771', '68600-000', 'Bragança', 'PA', 'Brasil', '', 1),
(83, 'Ana Claudia Rodrigues Dantas', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'AV.Marechal Castelo Branco, 611/ ap 601/Cabral', '(86) 9 9956-1011', '', 'Teresina', 'PI', 'Brasil', '', 1),
(84, 'Linésio Fonteles Machado', '0000-00-00', 0, 'Motorista SESC', 'brasileiro', 'masculino', NULL, '', '', '', 'AV. das Américas,500,BL.8/ sala315', '(21) 9 6479-8833', '', 'Rio de Janeiro', 'RJ', 'Brasil', '', 1),
(85, 'Fco Evaldo Mendes Sousa', '0000-00-00', 0, 'eletricista', 'brasileiro', 'masculino', NULL, '', '', '', 'AV.Onofre Cerqueira 871/ centro', '(86) 9 8134-8651', '', 'Piracuruca', 'PI', 'Brasil', '', 1),
(86, 'Eriane Alves de Sousa', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Rua Grijalma Carneiro s/n/centro', '(86) 9 9439-6844', '', 'Cajueiro da Praia', 'PI', 'Brasil', '', 1),
(87, 'Thiago de Castro Coelho', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'R.Clóvis Holanda de Freitas s/n dst/.tapera', '(85) 9 8556-0750', '', 'Aquiraz', 'CE', 'Brasil', '', 1),
(88, 'Marcos Machado  De Morais', '1982-01-20', 43, 'Representante', 'brasileiro', 'masculino', '92346804304', '', '', 'SSSP/CE', 'Rua shirley Girão N°630', '(88) 9 9909-1957', '60.743-805', 'Fortaleza', 'CE', 'Brasil', '', 1),
(89, 'Luiz Carlos Oliveira Junior', '1980-04-12', 44, 'Portuario', 'brasileiro', 'masculino', '64333809304', '', '', 'SSP/MA', 'Rua santa laura N°28 santa cruz', '(98) 9 9170 1653', '65046-461', 'São luiz', 'MA', 'Brasil', '', 1),
(90, 'José Pinto de Araujo', '1944-09-23', 79, 'motorista', 'brasileiro', 'masculino', '03325407315', '', '', '', 'AV.Dom Bosco n 500', '(85) 98725-3169', '', 'Baturité', 'CE', 'Brasil', '', 1),
(91, 'Douglas Lira Barbosa fibra ótica', '1998-06-21', 26, 'Técnico de fusão', 'brasileiro', 'masculino', '07748551340', '', '', '', 'Rua. Rdo. de Aguiar Freitas 145', '(86) 9 9820-8223', '0', 'Piripiri', 'PI', 'Brasil', '', 1),
(92, 'Raimunda Gomes Moura', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Conatrac/ ', '(98) 9 9611-6233', '', 'São Luis', 'PI', 'Brasil', '', 1),
(93, 'Vinícius Mariano ', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Otacílio Fortes 6754', '(86) 9 9470-4035', '', 'Teresina', 'PI', 'Brasil', '', 1),
(94, 'Jessyka Gonçalves Andrade', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Rua do Limoeiro 163/ S.miguel', '(88) 9 9910-5438', '', 'Juazeiro do Norte', 'CE', 'Brasil', '', 1),
(95, 'Thiago Texeira Paiva', '0000-00-00', 0, 'Fotográfo', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua da paz/2000 Siqueira', '(85) 9 9932-1582', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(96, 'Renato Manso Ramos', '1989-04-26', 35, 'Jornalista', 'brasileiro', 'masculino', '08182530458', '', '', '', 'Rua Antônio Ivo, 340 - 60521-025', '+55 85 8553-6359', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(97, 'Adércio Caverzan', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Lucas do Rio Verde', '(65) 9 9907-9266', '', 'Lucas do Rio Verde', 'MT', 'Brasil', '', 1),
(98, 'Franssinete Gomes Soares', '0000-00-00', 0, 'Aposentada', 'brasileira', 'feminino', NULL, '', '', '', 'Residencial Tabata 11/Aurora', '(98) 9 9120-1850', '', 'São Luis', 'MA', 'Brasil', '', 1),
(99, 'Allan Átila -  (Pernoite)', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Diogo Correia 643/Henr.Jorge', '(85) 9 8756-5072', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(100, 'Juliana Chaves Mota', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'AV. Isabela Moreira Gomes 186/ Boa Vizinhança', '(88) 9 9260-1370', '', 'Sobral', 'CE', 'Brasil', '', 1),
(101, 'Williane Magalhães', '0000-00-00', 0, '', 'brasileira', 'feminino', NULL, '', '', '', 'Rua Nelson Machado, 481/Amad.Furtado', '(85) 9 9692-7544', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(102, 'Agenilson Chaves Melo', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Cohatrac. Cj.Itaguara 2', '(98) 9 9611-6233', '', 'São Luis', 'MA', 'Brasil', '', 1),
(103, 'Antônio Francisco de Sousa', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Travessa Santa Madalena', '(86) 9 8159-0785', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(104, 'Rogério de Azevedo', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Loteamento Conviver 15', '(88) 9 9784-2333', '', 'Guaraciaba do Norte', 'CE', 'Brasil', '', 1),
(105, 'José Valcenar de Oliveira', '0000-00-00', 0, '', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Deputado Amadeu Gomes 160 Altos/Centro', '(88) 9 9792-2283', '', 'Acaraú', 'CE', 'Brasil', '', 1),
(106, 'Valter Dantas da Silva', '1971-11-08', 52, 'Vendedor_Monteiro Borracha', 'brasileiro', 'masculino', '52560333368', '', '', '', 'Rua Sao Paulo 300 Franciscano/ Juazeiro do Norte C', '88999754619', '63050440', 'Juazeiro do Norte', 'CE', 'Brasil', '', 1),
(107, 'Analine Dos Santos Silva (Filha Sr. Fco. Assis e A', '0000-00-00', 0, 'Psicologa', 'brasileira', 'feminino', NULL, '', '', 'SSP/PI', 'Rua Prof. Darcy Araújo, 298/Centro ', '', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(108, 'Jozielma Cardoso', '0000-00-00', 0, '', '', 'feminino', NULL, '', '', '', 'Rua Travessa Manoel Mergulhao 23 Maracanau', '85 9 87919803', '64500-000', 'Maracanau', 'CE', 'Brasil', '', 1),
(109, 'Evilázio Rosa da Silva', '1988-10-26', 36, 'motorista de aplicativo', 'brasileiro', 'masculino', '00521160340', '', '', '', '', '85 9 9247-3600', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(110, 'Rubenildo Falcâo de Melo', '1962-02-05', 62, 'Engenheiro Agrônomo', 'brasileiro', 'masculino', '43946062415', '', '', '', 'Rua Deputado Sebastião Brasilino de Freitas 555/Pa', '', '60824-010', 'Fortaleza', 'CE', 'Brasil', '', 1),
(111, 'Elda Fontinele Tahim (CENTEC)', '1963-02-24', 61, 'Engenharia de pesca/Professora', 'brasileira', 'feminino', '31704930359', '', '', '', 'Rua Joaquim Nabuco 2769 /Dionisio Torres', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(112, 'Maria Celina Martins Aragão', '1982-05-11', 42, 'Coordenadora Administrativa', 'brasileira', 'feminino', '65369050325', '', '', '', 'Av. Tenente Jose Araújo, 2690 /Centro', '88 99921-4411', '', 'Ipú', 'CE', 'Brasil', '', 1),
(113, 'Marcia de Brito Feitosa (CENTEC)', '1990-02-09', 34, 'Jornalista', 'brasileira', 'feminino', '02982054329', '', '', '', 'Rua Joaquim Nabuco 2769 /Dionisio Torres', '85 99837 7171', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(114, 'Luana Bandeira  (CENTEC)', '1988-02-29', 36, 'Assessoria técnica', 'brasileira', 'feminino', '02996325397', '', '', '', 'Rua Sete Casa 151, José Walter', '', '', 'Fortaleza', 'CE', '', '', 1),
(115, 'Darcyla de Freitas Lima (CENTEC)', '1968-11-19', 55, 'Servidor público', 'brasileira', 'feminino', '32335024353', '', '', '', 'Rua Murilo Fernandes 430 Apto. 1201-Guararapes', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(116, 'Francisco Jairo Almeida de Sousa  (CENTEC)', '1978-08-30', 46, 'Motorista', 'brasileira', 'masculino', '75469820300', '', '', '', 'Rua Torres de Melo 342/Fortaleza', '', '', 'Fortaleza', 'CE', '', '', 1),
(117, 'Helder Andrade Alves', '1987-01-02', 37, 'Professor', 'brasileiro', 'masculino', '02650364378', '', '', '', 'Rua Emídio Lima 356/Centro', '', '', 'Campo-Maior', 'PI', 'Brasil', '', 1),
(118, 'Bruno Carvalho Lima', '0000-00-00', 0, '', 'brasileira', 'masculino', NULL, '', '', '', 'Rua 438 casa 91/segunda etapa conj. ceará', '(85)9 9951-3047', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(119, 'Talita Cruz Quirino', '1986-11-28', 37, 'Enfermeira', 'brasileira', 'feminino', '03224750316', '', '', '', 'Rua Januário Silvino Pereira 92/Vila Fátima', '88 9 9921-4524', '', 'Juazeiro do Norte', 'CE', 'Brasil', '', 1),
(120, 'Cintia Monteiro da Costa', '1995-04-16', 29, 'Analista Comercial', 'brasileira', 'feminino', '05738438302', '', '', '', 'Avenida Poty Velho 5206/ Santa Maria', '(86) 9 8809-4507', '64.012.770', 'Teresina', 'PI', 'Brasil', '', 1),
(121, 'Francisco Jefferson Souza do Nascimento', '1989-10-05', 35, 'Funcionario Público', 'brasileiro', 'masculino', '04621628348', '', '', '', 'Rua Dirceu Arcoverde 2360/Bairro Piaui', '(86) 9 9411-4938', '64208-130', 'Parnaíba', 'PI', 'Brasil', '', 1),
(122, 'Willame Emerson Brandão Silva', '0000-00-00', 0, 'Técnico de Engenharia', 'brasileiro', 'masculino', '01076223354', '', '', '', 'Rua Gov. Raimundo Arthur de Vasconcelos, 3043 Prim', '(86) 9 8130-1943', '64001-450', 'Teresina', 'PI', 'Brasil', '', 1),
(123, 'George Cavalcanti de Almeida', '1981-05-06', 43, 'Administrativo', 'brasileiro', 'masculino', '03893983490', '', '', '', 'Rua 48 número 097Jardim Paulista Baixo', '(81)9 8894-1449', '53409-600', 'Paulista-Pe', 'Pe', 'Brasil', '', 1),
(124, 'Francisco Higor Sousa Mendes', '1993-04-28', 31, 'Enfermeiro', 'brasileiro', 'masculino', '05677571377', '', '', '', 'Avenida Cel. Gilo / Ipu-Ce', '88 9 8176-9165', '622540-00', 'Ipu', 'CE', 'Brasil', '', 1),
(125, 'Francisco das Chagas de Resende Pereira', '1998-12-27', 25, 'Fotógrafo', 'brasileiro', 'masculino', '08188836346', '00000', 'rg', 'SSP/PI', 'Alto Alegre, 387 - Caraúbas do Piauí', '(86) 9 8193-1491', '', 'Caraúbas do Piauí', 'PI', 'Brasil', '', 1),
(126, 'Adeilson Silva Cordeiro', '1982-09-08', 42, 'Militar', 'brasileiro', 'masculino', '71421920204', '00000', 'rg', '0', 'Rua Santa Maria, 38 - Atalaia', '(91) 9 8400-1632', '', 'Belém-Pa', 'PA', 'Brasil', '', 1),
(127, 'CARAVANA POUSADA GIRASSOL', '0000-00-00', 0, 'Excursão', 'brasileiro', 'feminino', NULL, '', '', '', 'Fortaleza-Ce', '(88) 9 9725-1741', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(128, 'Pedro Mileno de Assis', '1987-04-22', 37, 'Representante de semi-jóias', 'brasileiro', 'masculino', '01403942307', '', '', '', 'Av. Vladimir Rêgo Monteiro 1224 - São João', '86 9 9408-5132', '', 'Teresina', 'PI', 'Brasil', '', 1),
(129, 'Ronaldo Pereira de Brito', '1975-10-02', 49, 'Representante Semi-jóias', 'brasileiro', 'masculino', '78990874300', '', '', '', 'Rua Santa Teresinha, 155 / Bairro Santa Teresinha', '86 9 8100-5701', '64.235.000', 'Cocal da Estação-Pi', 'PI', 'Brasil', '', 1),
(130, 'Edna Maria Silva Santos', '1959-01-13', 65, 'Escritora/Psicóloga/Poeta', 'brasileira', 'feminino', '15099156304', '271.361', 'RG', 'SSP/PI', 'Povoado Cacimbão - Zona Rural de Parnaíba-Pi', '(86)9 9962-0770', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(131, 'Fabiano Ponte Aguiar', '1974-03-07', 50, 'ENGENHEIRO DE PESCA', 'brasileiro', 'masculino', '13565285893', '162508', 'RG', 'SSP/PI', 'Rua Ana Jenifer, 494 Parnaíba-Pi', '86 9 9436-5400', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(132, 'Andréa Rezende Pinheiro (Contratante)', '0000-00-00', 0, 'Arquiteta', 'brasileira', 'feminino', NULL, '950023032', 'RG', 'SSSP/CE', 'Rua Crisanto Moreira da Rocha 2500/Centro', '85 9 9124-5004', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(133, 'Railson Castro de Sousa', '0000-00-00', 0, 'Advogado', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Fortaleza, 25 /Planalto Turu 1 ', '(98)9 8872-2679', '', 'São Luis-MA', 'MA', 'Brasil', '', 1),
(134, 'Junior amigo Tiago', '0000-00-00', 0, 'Empresária', 'brasileiro', 'masculino', NULL, '', '', '', 'R. Gurupa, 7820', '86 988354049', '', 'Teresina', 'PI', 'Brasil', '<EMAIL>', 1),
(135, 'Francisco das Chagas Martins', '0000-00-00', 0, 'Militar', 'brasileiro', 'masculino', '', '', '', '', 'AV. Paizinho Sabiá n 557 - Bairro Centro', '88 9 8166-1221', '', 'juazeiro/sobral', 'CE', 'Brasil', '', 1),
(136, 'Júlio Cesar Rodrigues', '0000-00-00', 0, 'Ciclista', 'brasileiro', 'masculino', '', '', '', '', 'Olhos D\' água dos Balduínos', '86 9929-2489', '86 9943-6282', 'Caraúbas do Piauí', 'PI', 'Brasil', '', 1),
(137, 'Gleice Regina C. Cabral', '0000-00-00', 0, 'Representante comercial', '', 'feminino', '17899211808', '22.285.971-4', 'rg', '', 'R. Gato Cinzento, 60- VL Urupes', '11 964278582', '', 'Suzano', 'SP', 'Brasil', '<EMAIL>', 1),
(138, 'Francisco de Assis Vieira da Silva', '1957-09-22', 67, 'Administrador de Empresas', 'brasileiro', 'masculino', '29381738300', '', '', '', 'Rua Prof. Darcy Araújo, 298/ Centro', '(86) 9 9937- 7958', '', 'Luís Correia', 'PI', 'Brasil', '', 1),
(139, 'Francisco Lúcio Martins', '0000-00-00', 0, '', '', '', NULL, '', '', '', 'Sítio Sabiá', '', '', 'Ubajara', 'CE', 'Brasil', '', 1),
(140, 'Miguel Arcanjo Silva', '0000-00-00', 0, '', '', '', '', '', '', '', 'Serrinha', '85 9 9782-6312', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(141, 'Iara Cristina B. da Luz', '0000-00-00', 0, 'Professora', 'brasileira', 'feminino', '', '', '', '', '', '85 9 98165094', '', 'Frederico Westphalen', 'RS', 'Brasil', '', 1),
(142, 'Adêmio Oliveira Santos', '0000-00-00', 0, 'Aposentado', 'brasileiro', 'masculino', '05530857191', '', '', '', 'Q5 8 CJ C/23 Riacho Fundo 2', '61 9 9657-0096', '', 'Brasília', 'DF', 'Brasil', '<EMAIL>', 1),
(143, 'Nara Caroline dos Santos Silva (Filha Ana Maria)', '1991-04-04', 33, 'Psicóloga', 'brasileira', 'feminino', '', '', '', '', 'Rua Prof. Darcy Araújo 298 / Centro - Luís  Correi', '(351)9 60146640', '', 'Braga-Portugal', 'Po', 'Portugal', '', 1),
(144, 'Reginaldo dos Santos Lima', '0000-00-00', 0, 'Psicólogo', 'brasileiro', 'masculino', '', '2005007069628', 'RG', '', 'AV.l.710- Vila Velha', '85 9 9735-8239', '', 'Fortaleza', 'CE', 'Brasil', '<EMAIL>', 1),
(145, 'Renne de Carvalho', '0000-00-00', 0, 'Contador', '', '', '', '', '', '', 'Itaúna n 1021', '86 9 9966-5124', '', 'Parnaíba', 'PI', 'Brasil', '', 1),
(146, 'Francisco Evaristo dos Santos (Pai Ana Maria)', '0000-00-00', 0, 'Aposentado', 'brasileiro', 'masculino', NULL, '', '', '', 'Rua Prof. Darcy Araújo, 290/Centro ', '', '', 'Luís Correia-Pi', 'PI', 'Brasil', '', 1),
(147, 'José Jorge Bastos Vidigal', '1954-07-24', 70, 'Professor/Geociências', 'brasileiro', 'masculino', '07507461300', '', '', '', 'Rua Professor Darcy Araújo, 290/Centro', '', '', 'Luís Correia-Pi', 'PI', 'Brasil', '', 1),
(257, 'André Freitas da Silva/Fibra ótica', '1989-07-18', 35, 'Técnico de fusão', 'brasileiro', 'masculino', '04115567384', '', '', '', 'Rua 31 de Março núm S/N Pedreiras', '88 9 9828-6006', '', 'Varjota', 'CE', 'Brasil', '', 1),
(258, 'Gilvaldo de Oliveira Dias/Sebrae sem fome', '1988-09-25', 36, 'Gesseiro', 'brasileiro', 'masculino', '05352293300', '', '', '', 'Vila Oiticiquinhas S/N Zona Rural', '88 9 9754-3536', '', 'Independência', 'CE', 'Brasil', '', 1),
(259, 'Cátia Mendes de Moura', '1969-12-10', 54, 'Corretora de Imóveis', 'brasileira', 'feminino', '66682681534', '1009218', 'RG', 'SSP/PI', 'Q- 23 casa 10 conjunto Saci ', '86 9 9983-6554', '64.020-270', 'Teresina', 'PI', 'Brasil', '<EMAIL>', 1),
(265, 'Zeza dos Teclados', '1988-04-15', 36, 'Cantor', 'brasileiro', 'masculino', '02247373330', '', '', '', 'Rua 06 123 Jandaiguaba', '85 9 9280 3154', '', 'Caucaia', 'ce', 'Brasil', '', 1),
(266, 'Yuri Martins Sampaio', '0000-00-00', 0, 'Empresario', 'brasileiro', 'masculino', '04486880340', '', '', '', 'Rua Martins Neto 934- Antônio Bezerra', '', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(267, 'Welton Sales Meneses', '1985-04-04', 39, 'Montador Banco Brasil', 'brasileiro', 'masculino', '01114303305', '', '', '', 'Rua Passeio dos Cajueiros 1349-Cidade 2000', '8598687-3565', '60.191-665', 'Fortaleza', 'CE', 'Brasil', '', 1),
(268, 'Geraldo Ferreira da Silva', '1971-05-10', 53, 'Montador de Moveis BB', 'brasileiro', 'masculino', '41154320391', '', '', '', 'Rua Passeio dos cajueiros 1349-Cidade 2000', '(85) 9 9949-7332', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(269, 'Fabio Araújo de Sousa', '1983-07-18', 41, 'Vendedor Wolswagem', 'brasileiro', 'masculino', '94576858300', '', '', '', 'Conj. Residencial Alvorada Rua C Casa 07', '86 9 9558-0435', '64.207-120', 'Parnaíba', 'PI', 'Brasil', '', 1),
(270, 'Heloisa Ataide ', '1992-05-03', 32, 'Advogada', 'brasileira', 'feminino', '04944507380', '', '', '', 'Rua Miguel Calmon 220 Apto. 102 - Vicente Pinzon', '85 99991-8502', '60182160', 'Fortaleza', 'CE', 'Brasil', '', 1),
(271, 'Camila Monteiro da Costa', '1998-01-15', 26, 'Recepcionista-Cliniva Villar', 'brasileira', 'feminino', '07489443385', '', '', '', 'Av. Treze de Maio 2473-Vermelha', '869 8848-0860', '64.001-150', 'Teresina', 'PI', 'Brasil', '', 1),
(272, 'keilanny da silva sousa', '1977-10-14', 47, 'auxiliar administrativo', 'brasileira', 'feminino', '80907687334', '80907687334', 'RG', 'SSP/PI', 'rua antonio rodrigues machado', '99985196924', '65636535', 'timon', 'ma', 'brasil', '<EMAIL>', 1),
(273, 'Antonio Alves de Andrade Junior', '1980-04-27', 44, 'Servidor Pub Estadual', 'Brasileiro', 'Masculino', '86952960325', '', '', '', 'Rua Barroso 990 - vermelha', '86988406123', '64.018-520', 'Teresina', 'PI', 'Brasil', '', 1),
(274, 'Valdejan Carneiro de Mesquita', '1967-09-17', 57, 'Policial Civil', 'brasileiro', 'masculino', '27947092320', '', '', '', 'Estrada Campo Sao Francisco S/N -Vila Nair/Rancho ', '99 9 8136-1314', '', 'Barra do Corda ', 'MA', 'Brasil', '', 1),
(275, 'Orlandismar Marques de Almeida', '1996-06-25', 28, 'Tec. Telecomunicaçoes', 'brasileiro', 'masculino', '05877783181', '', '', '', 'Samabaia Sul, QS 304 Conj. 03 lote 05 Bl. A Apto. ', '61 9 9673-2956', '72.306-503', 'Brasilia', 'DF', '', '', 1),
(276, 'Tarcísio Pedrosa de Sousa', '1987-09-17', 37, 'Empresario', 'brasileiro', 'masculino', '02233638395', '', '', '', 'Rua Rio Juruá 328 - Tresidela', '99 98267612', '65.950.000', 'Barra do Corda', 'MA', 'Brasil', '', 1),
(277, 'Johnatas Lima dos Santos', '1982-01-12', 42, 'Pastor/Montese', 'brasileiro', 'masculino', '92655831349', '', '', '', 'Rua Nova 29-Vila Coelho Dias', '99 9 8182-5871', '65.700.000', 'Bacabal', 'MA', 'Brasil', '', 1),
(278, 'Marcílio Caetano de Oliveira', '1977-08-28', 47, 'Funcionário Público', 'brasileiro', 'masculino', '74756370349', '', '', '', 'Rua Padre Roma 800 Ap 202 -Fátima', '85 9 9148-7816', '600.493-60', 'Fortaleza', 'CE', 'Brasil', '', 1),
(279, 'Francisco Diego Silva Farias', '1991-03-19', 33, 'Servidos Publico', 'brasileiro', 'masculino', '04108478363', '', '', '', 'Avenida 01 N° 251-Maracanaú/Ce', '085 9 9755-3525', '61.900-410', 'Fortaleza', 'CE', 'Brasil', '', 1),
(280, 'Mauro Sérgio da Costa Oliveira (Amigos Cila)', '1976-01-03', 49, 'Empresário', 'Brasileiro', 'Masculino', '74370472334', '', '', '', 'Rua 18 N-4731/Manuel Evangelista', '86 9 9533-6371', '64.000-000', 'Teresina', 'PI', 'Brasil', '', 1),
(281, 'Antõnio Evaristo Araújo', '1960-10-27', 64, 'Fazendeiro', 'Brasileiro', 'Masculino', '18260802368', '', '', '', 'Rua 01 C-01 Cohaf Alto São FRancisco', '', '65.350-000', 'Vitória do Mearim', 'MA', 'Brasil', '', 1),
(282, 'Elvisley dos Anjos de Araújo', '1987-12-17', 37, 'Informatica do Grupo Matheus', 'Brasileiro', 'Masculino', '03190771367', '', '', '', 'Rua 11 C-24 Q-21 Primavera/Cohatrac', '', '65.052-857', 'São Luis', 'MA', 'Brasil', '', 1),
(283, 'Joel Pereira de Azevedo', '0000-00-00', 0, 'Empresário', 'Brasileiro', 'Masculino', NULL, '', '', '', 'Moitas-Amontada', '(88) 9 8155-6688', '', 'Amontada', 'CE', 'Brasil', '', 1),
(284, 'Janderson Almeida Azevedo', '1994-10-12', 30, 'Empresário (Pastelaria)', 'Brasileiro', 'Masculino', NULL, '', '', '', 'Moitas-Amontada-Ce', '88 9 8108-1499', '', 'Amontada', 'CE', '', '', 1),
(285, 'Luís Alberto Cavalcante de Paiva', '0000-00-00', 0, 'Aposentado', 'Brasileiro', 'Masculino', NULL, '', '', '', 'Rua Dr. Moisés Pimentel Neto Q-A C-04 / Campestre', '86 9 9987-4058', '64.053-510', 'Teresina', 'PI', 'Brasil', '', 1),
(286, 'Francisco das Chagas Santos de Melo', '1984-01-31', 40, 'Empresário', 'Brasileiro', 'Masculino', '00216892376', '', '', '', 'Rua Vereador Antonio Felipe Silva, 144 - Centroo', '86 9 9957-8191', '64.260.000', 'Piripiri', 'PI', 'Brasil', '', 1),
(287, 'Gessé Klayton sa Silva Alves', '1984-03-24', 40, 'Empresário', 'Brasileiro', 'Masculino', '00468549323', '', '', '', 'Rua Cornélio Círcero Mendes, 450-Recreio', '869 9996-2110', '64.260-000', 'Piripiri', 'PI', 'Brasil', '', 1),
(288, 'Francisco Glauber Pereira Ferreira', '1982-10-17', 42, 'Agente de vendas', 'Brasileiro', 'Masculino', '01104437309', '', '', '', 'Rua Capitão Manoel de Olveira 507 - Centro', '86 9 9924-8568', '64.260-000', 'Piripiri- PI', 'PI', 'brasil', '', 1),
(291, 'Leonardo José do Nascimento', '1991-09-24', 33, 'Administrador de Empresas', 'brasileiro', 'Masculino', '04290148301', '', '', '', 'Rua 13 138 lot Alto da Boa Vista- São Bento', '', '60.875-275', 'Fortaleza', 'CE', '', '', 1),
(292, 'José Milton Moreira Lima', '1957-11-02', 67, 'Comerciante', 'Brasileiro', 'Masculino', '16698371300', '', '', '', 'Rua Doutor Raimundo Guimaraes , 477 - Tamatanduba', '85 9 8151-2125', '61.768-430', 'Euzébio', 'CE', 'Brasil', '', 1),
(293, 'André Pereira Pitta Pinheiro', '1972-03-28', 52, 'Construtor', 'Brasileiro', 'Masculino', '42687209368', '', '', '', 'Rua Ministro Eduardo Eleri Barreira ,30 Apto. 502-', '859 9981-6922', '', 'Fortaleza', 'CE', 'Brasil', '', 1),
(294, 'Lucivandro de Castro Leitão', '1969-02-22', 55, 'CONFERENTE', 'Brasileiro', 'Masculino', '65022769387', '', '', '', 'RUA HONORATO SILVA 880', '(85)9 9946-2986', '60.873-060', 'Fortaleza', 'CE', 'Brasil', '', 1),
(295, 'JOSÉ LEONDAS MOREIRA MAIA', '1973-12-12', 51, 'EMPRESÁRIO', 'Brasileiro', 'Masculino', '54603234368', '', '', '', 'RUA SANTA RITA DE CÁSSIA 100-URUBURETAMA-EUSÉBIO', '(85)9 9946-2986', '60.835-226', 'Fortaleza', 'CE', 'Brasil', '', 1),
(296, 'MARDÕNIO  FREIRE DE OLIVEIRA', '1977-08-10', 47, 'EMPRESÁRIO', 'BRASILEIRO', 'MASCULINO', '73343609315', '', '', '', 'RUA NOSSA SENHORA APARECIDA 580-A / JARDIM BANDEIR', '(85)9 8655-4630', '', 'MARANCANAÚ', 'CE', 'Brasil', '', 1),
(297, 'MARIA JOSIANE MENDES CARVALHO', '1983-07-17', 41, 'PROFESSÔRA', 'BRASILEIRO', 'FEMININO', '00713100370', '', '', '', 'RUA MARIA SOUZA SALES 56-AMADOR', '(85)98809-9785', '61.760-000', 'EUZÉBIO - CEARÁ', 'CE', 'Brasil', '', 1),
(298, 'HÉLIO CORREIA LIMA', '0000-00-00', 0, 'EMPRESÁRIO-CONTADOR', '', '', '21820058387', '', '', '', 'AV. SÃO SEBASTIÃO 5170-FREI HIGINO', '86 9 9418-8141', '64.207-005', 'PARNAÍBA', 'PI', 'Brasil', '', 1),
(299, 'CAMILA MARIA  BATISTA BASTOS', '1985-12-03', 39, 'ENFERMEIRA', 'BRASILEIRO', 'FEMININO', NULL, '', '', '', 'RUA CANADÁ 1415/CRISTO REI', '(86)98822-7584', '64.014-415', 'TERESINA-PI', 'PI', 'BRASIL', '', 1),
(300, 'ROGÉRIO GAMA BASTOS', '0000-00-00', 0, 'Autônoma', 'BRASILEIRO', 'MASCULINO', '20774664304', '', '', '', 'CONJUNTO CRISTO REI 46/BAIRRO CRISTO REI', '(86)9 8822-7584', '64.014-540', 'TERESINA-PI', 'PI', 'BRASIL', '', 1),
(301, 'ISABELLA RAQUEL BASTOS MORAIS', '2009-11-13', 15, 'ESTUDANTE', 'BRASILEIRO', 'FEMININO', NULL, '', '', '', 'RUA CANADÁ 1415/CRISTO REI', '(86)9 8822-7584', '64.014-415', 'TERESINA-PI', 'PI', 'BRASIL', '', 1),
(302, 'MARCEL DE ARAÚJO FERREIRA', '1988-01-06', 37, 'AUTÔNOMO', 'BRASILEIRO', 'MASCULINO', '00932703305', '', '', '', 'RUA MANOEL SALES 166 CENTRO', '(88)9 9900-0220', '62.580-000', 'ACARAÚ', 'CE', 'BRASIL', '', 1),
(303, 'Francisco das Chagas Marques da Silva', '1984-08-08', 40, 'Empresário', 'BRASILEIRO', 'MASCULINO', '96643315387', '', '', '', 'RUA ARIMATÉIA TITO, 762-MONTE CASTELO', '86 9 9906-1245', '64.016-190', 'Teresina', 'PI', 'BRASIL', '', 1),
(304, 'RAIANNY CRISTINA RAMOS LIMA', '1993-02-22', 32, 'FISIOTERAPEUTA', 'BRASILEIRO', 'FEMININO', '05283712311', '', '', '', 'AVENIDA SÃO SEBASTIÃO 5170 / FREI HIGINO', '86 9 9903-3535', '64.207-005', 'PARNAÍBA', 'PI', 'BRASIL', '', 1),
(305, 'ANA CARLA RODRIGUES DE OLIVEIRA', '1985-05-05', 39, 'ASSISTENTE DE RECURSOS HUMANOS', 'BRASILEIRO', 'FEMININO', '02026735379', '', '', '', 'AVENIDA PADRE JOSÉ HOLANDA DO VALE 600 MARACANAÚ', '85 9 9655-7826', '61.910-478', 'Maracanaú', 'CE', 'BRASIL', '', 1),
(306, 'LEONARDO COSTITE NICOLICHE', '1989-06-21', 35, 'VENDEDOR AUTÔNOMO', 'BRASILEIRO', 'MASCULINO', '02861409509', 'campo dos velhos ', 'rua padre anchieta 1', '', 'RUA TITO SOARES 2026 - MACAMBIRA', '77 9 9862-7888', '62.030-972', 'SANTA MA. VITÓRIA', 'BA', 'BRASIL', '<EMAIL>', 1),
(307, 'FÁBIO PIRES DO COUTO FILHO', '1970-11-01', 54, 'ADM DE EMPRESAS', 'BRASILEIRO', 'MASCULINO', '68637004687', '', '', '', 'RUA ANDORINHAS 5 APTO. 105 PQ ATLÂNTICO', '31 9 8886-7567', '65.065-670', 'SÃO LUÌS', 'MA', 'BRASIL', '', 1),
(308, 'WITAÇUCI KHLEWDYSON REIS BEZERRA', '1973-12-31', 51, 'PROFESSOR', 'BRASILEIRO', 'MASCULINO', '56293232372', '', '', '', 'RUA 04 CASA 15 RESIDENCIAL ECO BUGANVILLE - ARAÇAG', '', '65.110-000', 'SÃO LUÍS', 'MA', 'BRASIL', '', 1),
(309, 'SABRINA SETÚBAL', '2001-03-26', 24, 'ESTUDANTE', 'BRASILEIRO', 'FEMININO', '06724525373', '', '', '', 'RUA JUVÊNCIO GOMES DE FREITAS 266 ALTO NELANDIA', '88 9 9921_6536', '', 'TAUA', 'CE', 'BRASIL', '', 1),
(310, 'SILVANA TEÓFILO MACHADO', '0000-00-00', 0, 'PROFESSORA', 'BRASILEIRO', '', '23363363320', '', '', '', 'RUA SANDRA GENTIL/FREI JOSÉ MARIA 1100', '', '60.833-291', 'FORTALEZA', 'CE', '', '', 1),
(311, 'MAYARA SANTOS DUTRA', '1991-08-20', 33, 'ANALISTA DE RH', 'BRASILEIRO', 'FEMININO', '03994553377', '', '', '', 'AV. GENERAL ARTUR CARVALHO COND.JARDINS 5 BL-03 AP', '98 9 8770-5891', '65.110-000', 'SÃO JOSÉ DE RIBAMAR', 'MA', 'BRASIL', '', 1),
(312, 'SÔNIA MARIA DIAS DE SOUSA', '0000-00-00', 0, '', 'BRASILEIRO', 'FEMININO', '30698693353', '', '', '', 'CONJUNTO REDENÇÂO Q-D CASA 15', '86 9 9829-0713', '', 'Teresina', 'PI', '', '', 1),
(510, 'THAMIRES ARCÂNJO MELO', '0000-00-00', 0, 'ENFERMEIRA', 'BRASILEIRO', 'FEMININO', '04174995311', '', '', '', 'RUA PIRES REBELO 161-CENTRO', '86 9 9995-2251', '64.260-000', 'PIRIPIRI', 'PI', '', '', 1),
(511, 'APOLLYANNE DE FÁTIMA DE SOUSA GOMES', '1988-06-30', 36, 'AGENTE DE COMBATE AS ENDEMIAS', 'BRASILEIRO', 'FEMININO', '02671332351', '', '', '', 'LOT. CONSELHEIRO ALBERTO SILVA QD-09 C-07', '86 9 9990-3307', '', 'PARNAÍBA', 'PI', 'BRASIL', '', 1),
(512, 'CARLA JAMILLY ARAÚJO VERAS', '2001-10-11', 23, 'ESTETICISTA', 'BRASILEIRO', 'FEMININO', '08420763365', '', '', '', 'RUA CORONEL JOSÉ PORFÍRIO 479-CENTRO', '88 9 8822-2747', '62.420-000', 'CHAVAL', 'CE', 'BRASIL', '', 1),
(513, 'MARIA DA CONCEIÇÃO TEIXEIRA MELO', '1964-09-08', 60, 'PROFESSORA', 'BRASILEIRO', 'FEMININO', '33928088300', '', '', '', '', '869 9920-5025', '64.260.000', 'Parnaíba', 'PI', '', '', 1),
(514, 'EDMILSON SOUSA FERRO ', '1943-04-08', 82, 'APOSENTADO CHESF', 'BRASILEIRO', 'MASCULINO', '02365260306', '', '', '', 'ROD. VICENTE FIALHO KM 03 SITIO AUGUSTO E ATENAS', '86 9 9991 9457', '64.260.000', 'PIRIPIRI', 'PI', 'BRASIL', '', 1),
(515, 'JOLNÊ BINDÁ PRAXEDES', '1960-12-31', 64, 'CORRETOR DE IMÓVEIS', 'BRASILEIRO', 'MASCULINO', '18563473549', '', '', '', 'RUA ROCHA LIMA 260-CENTRO', '85 9 9814-5643', '', 'Fortaleza', 'CE', 'BRASIL', '', 1),
(518, 'PEDRO WILLYS LOPES DA SILVA', '0000-00-00', 0, 'MOTORISTA DE ROTA', 'BRASILEIRO', 'MASCULINO', '', '', '', '', 'RUA PROJETADA DUZENTOS E DEZ NUMERO 20-FREI HIGINO', '86 9 8145-0209', '', 'Parnaíba', 'PI', 'BRASIL', '', 1),
(519, 'MARIA MAIARA DE CARVALHO', '0000-00-00', 0, '', '', 'FEMININO', '', '', '', '', '', '', '', 'NOVA RUSSAS', 'CE', 'BRASIL', '', 1),
(520, 'LAISA RANIELLE BARBOSA', '1997-10-04', 27, 'EMPRESARIA DO LAR', 'brasileira', 'FEMININO', '13647357413', '', '', '', 'SITIO SANUNGA S/N', '88 9 8191-7326', '', 'URUOCA', 'CE', '', '', 1),
(521, 'ADEMIR PIGATTI', '1974-10-12', 50, 'MINERADOR', 'BRASILEIRO', 'MASCULINO', '04220674748', '', '', '', 'CACHOEIRA DO ITAPEMIRIM-MARBRAZA, 36', '', '', 'ESPÍRITO SANTO', 'VI', 'BRASIL', '', 1),
(522, 'GARCIA  MARTINEZ (ESPANHOL)', '1952-12-03', 72, 'FAZENDEIRO', 'ESPANHOLA', 'MASCULINO', '', '', '', '', 'OLHO DÀGUA-MUNICÍPIO DE COCAL', '081 99537-5813', '', 'COCAL DA ESTAÇÃO', 'PI', 'BRASIL', '', 1),
(523, 'JOSÉ CLEYLTON ALVES DA SILVA', '0000-00-00', 0, 'MECÃNICO', 'BRASILEIRO', '', '', '', '', '', 'RUA CHICO SALES, 855-BAIRRO IMACULADA CONCEIÇÃO 11', '85 9 8632-5121', '', 'CANINDÉ', 'CE', 'BRASIL', '', 1),
(524, 'SAMILE DA SILVA TEIXEIRA', '1998-04-27', 27, 'AUTONOMA', 'BRASILEIRO', 'FEMININO', '07097332328', '', '', '', 'SITIO LADEIRA GRANDE S/N', '85 9 9439-4705', '', 'VIÇOSA DO CE', 'CE', 'Brasil', '', 1),
(525, 'MIRIAN RODRIGUES NETO', '1997-01-19', 28, 'Autônoma', 'BRASILEIRO', 'FEMININO', '06642107322', '', '', '', 'AV. PRES.CASTELO BRANCO 3065 CENTRO', '889 9704-9713', '62.265-000', 'VARJOTA', 'CE', 'BRASIL', '', 1),
(526, 'ANA LÚCIA DA SILVA', '0000-00-00', 0, 'DECORADORA DE FESTAS', 'BRASILEIRO', 'FEMININO', '', '', '', '', 'CONJ. DOM HELDER Q-D C-23/PARQUE IDEAL/DIRCEU I', '86 9 9801-9143', '64.000-000', 'TERESINA-PI', 'PI', 'BRASIL', '', 1),
(527, 'MARCELO DO NASCIMENTO LOPES', '0000-00-00', 0, 'MOTORISTA/EVENTOS', 'BRASILEIRO', 'MASCULINO', '', '', '', '', 'CONJ.DOM HELDER Q-D C-23/P.IDEAL/DIRCEU I', '86 9 9801-9143', '64.000-000', 'Teresina', 'PI', 'BRASIL', '', 1),
(529, 'AULECI DA SILVA SIQUEIRA BARBOSA', '1977-07-11', 47, 'PROFESSORA', 'BRASILEIRO', 'FEMININO', '63279959349', '', '', '', 'RUA CONS. ARA´JO LIMA 1645 - DOM LUSTOSA', '', '60.526-075', 'Fortaleza', 'CE', 'BRASIL', '', 1),
(530, 'ROBSON SILVA CARDOSO', '0000-00-00', 0, 'BANCÁRIO', 'BRASILEIRO', 'MASCULINO', '98833898334', '', '', '', 'RUA VALDEMIRO CAVALCANTE 394 RODOLFO TEÓFILO', '', '', 'FORTALEZA', 'CE', 'BRASIL', '', 1),
(531, 'VIRGÍNIA MARIA BERTOLDO', '0000-00-00', 0, '', '', 'FEMININO', '', '', '', '', 'RUA DESEMBARGADOR CARVALHO LIMA 178 JARDIM DAS OLI', '', '', 'FORTALEZA', 'CE', 'BRASIL', '', 1),
(532, 'ALVARO MAURÍCIO DUARTE FREITAS', '1993-03-17', 32, 'EMPRESÁRIO', 'BRASILEIRO', 'MASCULINO', '', '', '', '', 'RUA OTACÍLIO DE SOUSA MOREIRA 04/B.N.S.DA SAÚDE', '85 9 9211-7169', '62.685-000', 'PARAIPABA', 'CE', 'BRASIL', '', 1),
(533, 'BRÁS CLEITON DE JESUS SOUSA', '0000-00-00', 0, '', '', 'MASCULINO', '', '', '', '', '', '', '', 'PARAIPABA-CE', 'CE', 'BRASIL', '', 1),
(534, 'GUSTAVO LOHAM BORGES SOUSA', '0000-00-00', 0, '', '', 'MASCULINO', '', '', '', '', '', '', '', 'PARAIPABA-CE', 'CE', '', '', 1),
(535, 'MARIA DOSOCORRO MONTE SILVA (BEBEL NOIVAS TERESINA', '1960-09-15', 64, 'APOSENTADA', 'BRASILEIRO', 'FEMININO', '15627411368', '', '', '', 'QUADRA 45 C-24-DIRCEU I/BAIRRO ITARARÉ', '86 9 9973-3665', '', 'TERESINA', 'PI', 'BRASIL', '', 1),
(536, 'FÁTIMA LETÍCIA GONÇALVES', '1953-01-17', 72, 'PROF.UNIVERSITÁRIA', 'BRASILEIRA', 'FEMININO', '11788780310', 'ID-549126', 'RG', 'SSP-CE', 'RUA UBAJARA-PQ ALBANO, 2278 JUREMA', '85 9 9956-7777', '61.600.000', 'CAUCAIA', 'CE', 'BRASIL', '', 1),
(537, 'EDGAR RODRIGUES DE SOUZA', '1965-11-27', 59, 'SERVIDOR PÚBLICO', 'BRASILEIRO', 'MASCULINO', '41054644187', '912.127-DF', 'RG', 'SSDF', 'SMDS-CHACRA 3 LOTE 1-A-GUARÁ II', '61 9 8633-6807', '71.080-030', 'BRASILIA-DF', 'DF', 'BRASIL', '', 1),
(538, 'JEFERSON MACEDO DE SOUZA', '1998-07-13', 27, 'POLICIAL', 'BRASILEIRO', 'MASCULINO', '', '', '', '', '', '', '', 'BRASILIA-DF', 'DF', 'BRASIL', '', 1),
(539, 'JAIRO PEREIRA DE AZEVEDO', '1981-11-25', 43, 'Empresário', 'BRASILEIRO', 'MASCULINO', '00212613340', '', '', '', '', '', '', 'MOITAS-AMONTADA', 'CE', 'BRASIL', '', 1),
(540, 'AGENI MARQUES DE FREITAS', '1987-10-10', 37, 'Autônoma', 'BRASILEIRO', 'FEMININO', '', '', '', '', '', '', '', 'MOITAS/ AMONTADA', 'CE', 'BRASIL', '', 1),
(541, 'ANA LENISE MELO JÚLIO', '1996-09-12', 28, '', 'BRASILEIRO', 'FEMININO', '06746922332', '', '', '', 'RUA VIRIATO DE MEDEIROS, 1377SOBRAL', '88 9 9698-6773', '62.011-065', 'SOBRAL', 'CE', 'BRASIL', '', 1),
(542, 'MAYARA SANTOS DUTRA', '1991-08-20', 33, 'ANALISTA DE RH', 'BRASILEIRA', 'FEMININO', NULL, '', '', '', 'AV.GEN.ARTUR CARVALHO S/N COND.VILLAGE JARDINS 5', '98 9 8770-5891', '65.123-385', 'SÃO JOSE DE RIBAMAR', 'MA', 'BRASIL', '', 1),
(543, 'SARA SILVA DOS SANTOS', '0000-00-00', 0, '', '', 'FEMININO', '', '', '', '', 'BECO PROJETADO DA RUA 30 2358/BAIRRO PARQUE PIAUI', '', '65.631-220', 'TIMON', 'MA', 'BRASIL', '', 1),
(544, 'JARDEL DAMASCENO RODRIGUES', '0000-00-00', 0, '', '', 'MASCULINO', '', '', '', '', 'AV. GETÚLIO VARGAS 853-SENADOR ', '', '', 'ARAGUAINA', 'TO', 'BRASIL', '', 1),
(545, 'JARDEL DAMASCENO RODRIGUES', '0000-00-00', 0, '', '', '', '', '', '', '', 'AV. GETÚLIO VARGAS, 853-BAIRRO SENADOR', '63 9 9236-0871', '77.813-505', 'ARAGUAÍNA', 'TO', 'BRASIL', '', 1),
(546, 'FAYROWS MOHAMAD EL AKHRAS', '0000-00-00', 0, '', '', 'FEMININO', '', '', '', '', '904 SUL ALAMEDA 14 LOTE 18 CASA 10 RESID. PEDRO LO', '', '', 'PALMAS ', 'TO', 'BRASIL', '', 1),
(547, 'RK TENDAS', '2024-09-16', 0, 'SERVIÇOS DE TENDAS', 'BRASILEIRO', '', '', '', '', '', 'RUA VICENTE ROSAL FERREIRA LEITE, 155-JANGURUSSU', '85 9139-9243', '60.877-498', 'FORTALEZA', 'CE', 'BRASIL', '<EMAIL>', 1);
INSERT INTO `hospedes` (`id`, `nome`, `nasc`, `idade`, `profissao`, `nacionalidade`, `sexo`, `cpf`, `documento`, `tipo`, `expedidor`, `endereco`, `telefone`, `cep`, `cidade`, `uf`, `pais`, `email`, `pousada_id`) VALUES
(548, 'SÁVIO GUIMARÃES DA CUNHA', '0000-00-00', 0, '', 'BRASILEIRA', 'MASCULINO', '06320714352', '', '', '', 'Q-21 BL 08 APTO 304-MORADA NOVA', '86 9477-3165', '64.023-204', 'TERESINA-PI', 'PI', 'BRASIL', '', 1),
(550, 'NILTON E VILMA', '0000-00-00', 0, '', 'BRASILEIRO', 'MASCULINO', '', '', '', '', '', '', '', 'Fortaleza', 'CE', 'BRASIL', '', 1),
(551, 'JULIANO E ÉRIKA', '0000-00-00', 0, '', 'BRASILEIRO', 'MASCULINO', '', '', '', '', '', '', '', 'Fortaleza', 'CE', 'BRASIL', '', 1),
(552, 'IVANALDO E KEILA', '0000-00-00', 0, '', 'BRASILEIRO', 'MASCULINO', '', '', '', '', '', '', '', 'Fortaleza', 'CE', 'BRASIL', '', 1),
(553, 'BARROSO E CILMA', '0000-00-00', 0, '', 'BRASILEIRO', 'MASCULINO', '', '', '', '', '', '', '', 'Fortaleza', '', 'BRASIL', '', 1),
(554, 'JMA ENGENHARIA SOLUÇÕES EM CAD LTDA', '0000-00-00', 0, 'ENGENHARIA', 'BRASILEIRA', '', '', '', '', '', '', '85 8705-1303', '', 'Fortaleza', 'CE', 'BRASIL', '', 1),
(555, 'MARCELLO JOSÉ BENEVIDES BORGES', '1971-07-27', 53, 'PROFESSOR', 'BRASILEIRO', 'MASCULINO', '42269857372', '94002320213', 'RG', 'SSPDC', 'RUA PROF.WILSON AGUIAR 131-EDSON QUEIROZ', '85 996466466', '60.811.590', 'Fortaleza', 'CE', 'BRASIL', '', 1),
(556, 'LAUNDA SOARES GRANJEIRO', '1986-08-21', 38, 'PROFESSORA', 'BRASILEIRA', 'FEMININO', '', '', '', '', 'RUA MARIA RIBEIRO 4853-MOCAMBINHO', '86 8811-0862', '64.010-660', 'TERESINA-PI', 'PI', 'BRASIL', '', 1),
(557, 'WLADSON DE QUEIROZ ALCANTARA', '0000-00-00', 0, 'PROFESSOR', 'BRASILEIRO', 'MASCULINO', '94074593300', '', '', '', 'RUA DAS LARANJEIRAS 1A AP 202 ALTO ALEGRE', '85 8897-1707', '61.700-000', 'AQUIRAZ', 'CE', 'BRASIL', '', 1),
(558, 'DANIEL DA SILVA ', '1987-08-16', 37, 'EMPRESARIO', 'BRASILEIRO', 'MASCULINO', '04150184399', '', '', '', 'AV. ATALIBA VIEIRA DE ALMEIDA 995-A TERRAS DURAS', '98  991771503', '65.500-000', 'CHAPADINHA -MA', 'MA', 'BRASIL', '', 1),
(559, 'CARLOS ANTONIO DE SOUSA VIANA', '1992-12-07', 32, 'BANCÁRIO', 'BRASILEIRO', 'MASCULINO', '05624563337', '', '', '', 'TERCEITA TRAVESSA  AUGUSTINHO RIBEIRO 517-AREAL', '98 9 9174-2207', '65.500-000', 'CHAPADINHA-MA', 'MA', 'BRASIL', '', 1),
(560, 'MARCOS DOS SANTOS FEITOSA', '1991-02-23', 34, 'MECANICO', 'BRASILEIRO', 'MASCULINO', '60439081319', '0378582720097', 'RG', 'SSP MA', 'RUA TRAVESSA KENEDDY  2958-P.INDEPENDENCIA', '', '65.500-000', 'CHAPADINHA -MA', 'MA', 'BRASIL', '', 1),
(561, 'WALMIR SEVERO MAGALHAES', '1955-07-31', 69, 'AGRONOMO', 'BRASILEIRO', 'MASCULINO', '10170685349', '', '', '', 'RUA FCA. RANGEL BEZERRA 545-PARQUELANDIA', '859 9934-7912', '', 'Fortaleza', 'CE', 'BRASIL', '', 1),
(562, 'PEDRO DE SOUSA MENESES', '1986-08-04', 38, 'ADVOGADO', 'BRASILEIRO', 'MASCULINO', NULL, '', '', '', 'RUA CANADA 1415 COND.CANADA RESIDENSE CLUB', '869 9825-0963', '64.014-415', 'TERESINA', 'PI', 'BRASIL', '', 1),
(563, 'BRENO ÁVILA SANTOS', '1991-02-02', 34, 'REPRESENTANTE DE VENDAS', 'BRASILEIRO', 'MASCULINO', '04984405325', '200.601.027.5532', 'RG', 'SSP/CE', 'RUA JURANDIR NUNES 476-SAPIRANGA', '85 9 9168-7893', '60.833.192', 'FORTALEZA', 'CE', '', '', 1),
(564, 'CARLOS ANDRÉ DE MORAES PEREIRA', '1991-11-01', 33, '', 'BRASILEIRO', 'MASCULINO', '60302822399', '', '', '', 'RUA FORTALEZA 203 CHACARA BRASIL', '99 8110-2445', '65.066-851', 'SÃO LUIZ-MA', 'MA', 'BRASIL', '', 1),
(565, 'ELLEN DO NASCIMENTO MONTEIRO', '1999-04-29', 26, '', 'BRASILEIRA', 'FEMININO', '07591474340', '', '', '', 'AV.MONS.JOSÉ ALOÍSIO PINTO, 325', '88 9346-5822', '62.051-225', 'SOBRAL', 'CE', 'BRASIL', '', 1),
(566, 'ANTONIO EVARISTO', '0000-00-00', 0, '', 'BRASILEIRO', 'MASCULINO', '', '', '', '', '', '', '', 'PARNAIBA-PI', 'PI', 'BRASIL', '', 1),
(567, 'JORGE VIDIGAL', '0000-00-00', 0, '', 'BRASILEIRO', 'MASCULINO', '', '', '', '', '', '', '', 'PARNAÍBA', 'PI', '', '', 1),
(568, 'DANIEL VIEIRA ', '0000-00-00', 0, '', 'BRASILEIRO', 'MASCULINO', '', '', '', '', '', '', '', 'PARNAÍBA', 'PI', 'BRASIL', '', 1),
(569, 'PLÍNIO MIRANDA LIMA', '0000-00-00', 0, '', 'BRASILEIRO', 'MASCULINO', '98514245368', '', '', '', 'RUA PARQUE SHALOM COND-ILHAS GREGAS BL-CRETA AP-05', '', '65.072-810', 'SÃO LUIZ-MA', 'MA', 'BRASIL', '', 1),
(570, 'Claudio Segura Correi', '1967-03-10', 58, 'Psicólogo', 'brasileira', 'masc', '06918723845', '544564564', 'este', 'sspce', 'Sítio Pará s/n', '(88) 99909 0222', '62300-000', 'Viçosa do Ceará', 'CE', 'Brasil', '<EMAIL>', 1),
(571, 'este', '0000-00-00', 0, '', '', '', '06918723847', '', '', '', '', '', '', '', '', '', '', 1);

-- --------------------------------------------------------

--
-- Estrutura para tabela `lancamentos_financeiros`
--

CREATE TABLE `lancamentos_financeiros` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `reserva_id` int DEFAULT NULL,
  `tipo` enum('receita','despesa') NOT NULL,
  `categoria_id` int NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `data_lancamento` date NOT NULL,
  `data_vencimento` date NOT NULL,
  `data_pagamento` date DEFAULT NULL,
  `status` enum('pendente','pago','cancelado') NOT NULL DEFAULT 'pendente',
  `forma_pagamento` varchar(50) DEFAULT NULL,
  `observacao` text,
  `usuario_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `lancamentos_financeiros`
--

INSERT INTO `lancamentos_financeiros` (`id`, `pousada_id`, `reserva_id`, `tipo`, `categoria_id`, `descricao`, `valor`, `data_lancamento`, `data_vencimento`, `data_pagamento`, `status`, `forma_pagamento`, `observacao`, `usuario_id`, `created_at`, `updated_at`) VALUES
(31, 1, 594, 'receita', 19, 'Hospedagem - PEDRO WILLYS LOPES DA SILVA - UH 003', 180.00, '2025-05-25', '2025-05-24', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-05-25 11:33:50', '2025-07-30 11:58:28'),
(32, 1, 595, 'receita', 19, 'Hospedagem - George de Castro e Silva (Pastor) - UH 002', 180.00, '2025-05-25', '2025-05-24', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-05-25 11:36:34', '2025-07-30 11:57:45'),
(33, 1, NULL, 'receita', 19, 'Hospedagem - LEONARDO COSTITE NICOLICHE - UH 007', 0.00, '2025-05-25', '0000-00-00', NULL, 'cancelado', NULL, '\nCancelado automaticamente devido ao cancelamento da reserva #596', 31, '2025-05-25 11:41:28', '2025-07-30 11:52:19'),
(34, 1, 597, 'receita', 19, 'Hospedagem - MARIA MAIARA DE CARVALHO - UH 008', 180.00, '2025-05-31', '2025-05-31', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-05-31 21:32:42', '2025-07-30 11:59:03'),
(35, 1, 598, 'receita', 19, 'Hospedagem - LAISA RANIELLE BARBOSA - UH 008', 180.00, '2025-06-07', '2025-06-06', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-06-07 11:37:08', '2025-07-30 11:59:36'),
(36, 1, 599, 'receita', 19, 'Hospedagem - ADEMIR PIGATTI - UH 004', 180.00, '2025-06-14', '2025-06-11', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-06-14 17:28:40', '2025-07-30 12:00:05'),
(37, 1, 600, 'receita', 19, 'Hospedagem - GARCIA  MARTINEZ (ESPANHOL) - UH 003', 400.00, '2025-06-14', '2025-06-14', '2025-07-30', 'pago', 'PIX', NULL, 31, '2025-06-14 17:34:05', '2025-07-30 12:01:47'),
(38, 1, 601, 'receita', 19, 'Hospedagem - JOSÉ CLEYLTON ALVES DA SILVA - UH 002', 360.00, '2025-06-14', '2025-06-13', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-06-14 17:38:53', '2025-07-30 12:00:34'),
(39, 1, 602, 'receita', 19, 'Hospedagem - Jakson Cavalcante De Mesquita - UH 004', 520.00, '2025-06-14', '2025-08-22', NULL, 'pendente', NULL, 'PAGOU RESERVA NO VALOR DE 270,00', 31, '2025-06-14 19:48:34', '2025-08-01 14:38:15'),
(40, 1, 603, 'receita', 19, 'Hospedagem - SAMILE DA SILVA TEIXEIRA - UH 005', 180.00, '2025-06-14', '2025-06-14', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-06-14 22:02:33', '2025-07-30 12:01:20'),
(41, 1, 604, 'receita', 19, 'Hospedagem - MIRIAN RODRIGUES NETO - UH 007', 180.00, '2025-06-15', '2025-06-14', '2025-07-30', 'pago', 'PIX', NULL, 31, '2025-06-15 12:31:52', '2025-07-30 12:00:58'),
(42, 1, 605, 'receita', 19, 'Hospedagem - ANA LÚCIA DA SILVA - UH 001', 220.00, '2025-06-28', '2025-06-27', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-06-28 11:25:37', '2025-07-30 12:02:31'),
(43, 1, 606, 'receita', 19, 'Hospedagem - MARCELO DO NASCIMENTO LOPES - UH 003', 220.00, '2025-06-28', '2025-06-27', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-06-28 11:34:46', '2025-07-30 12:02:09'),
(44, 1, 607, 'receita', 19, 'Hospedagem - MARCELO DO NASCIMENTO LOPES - UH 001', 150.00, '2025-06-28', '2025-06-28', '2025-07-30', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-06-28 11:36:20', '2025-07-30 12:02:55'),
(45, 1, NULL, 'receita', 19, 'Hospedagem - MAYARA CASTELO BRANCO - UH 002', 190.00, '2025-07-03', '2025-07-16', '2025-07-14', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-03 10:40:58', '2025-07-14 14:59:45'),
(46, 1, 609, 'receita', 19, 'Hospedagem - AULECI DA SILVA SIQUEIRA BARBOSA - UH 004', 400.00, '2025-07-05', '2025-07-07', '2025-07-10', 'pago', 'PIX BRADESCO OU CARTÃO DÉBITO/CRÉDITO-TON', NULL, 31, '2025-07-05 15:43:13', '2025-07-10 11:45:12'),
(47, 1, 610, 'receita', 19, 'Hospedagem - ROBSON SILVA CARDOSO - UH 003', 800.00, '2025-07-06', '2025-07-23', '2025-07-06', 'pago', 'CONTA BRADESCO', 'PAGOU 50% NA RESERVA', 31, '2025-07-06 23:18:05', '2025-07-14 15:02:03'),
(48, 1, 611, 'receita', 19, 'Hospedagem - VIRGÍNIA MARIA BERTOLDO - UH 008', 180.00, '2025-07-07', '2025-07-11', '2025-07-14', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-07 14:16:43', '2025-07-14 14:59:13'),
(49, 1, 612, 'receita', 19, 'Hospedagem - VIRGÍNIA MARIA BERTOLDO - UH 003', 270.00, '2025-07-07', '2025-07-11', '2025-07-14', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-07 14:21:56', '2025-07-14 14:58:26'),
(50, 1, 613, 'receita', 19, 'Hospedagem - VIRGÍNIA MARIA BERTOLDO - UH 005', 90.00, '2025-07-07', '2025-07-11', '2025-07-14', 'pago', 'PIX', NULL, 31, '2025-07-07 14:25:02', '2025-07-14 14:57:55'),
(51, 1, 614, 'receita', 19, 'Hospedagem - Francisco de Assis Vieira da Silva - UH 003', 190.00, '2025-07-07', '2025-07-12', '2025-07-13', 'pago', 'PIX', NULL, 31, '2025-07-07 14:47:50', '2025-07-13 13:29:37'),
(52, 1, 615, 'receita', 19, 'Hospedagem - Francisco de Assis Vieira da Silva - UH 008', 190.00, '2025-07-07', '2025-07-12', '2025-07-13', 'pago', 'PIX', NULL, 31, '2025-07-07 14:48:56', '2025-07-13 13:29:12'),
(53, 1, 616, 'receita', 19, 'Hospedagem - Àlvaro Maurício Duarte Freitas - UH 002', 380.00, '2025-07-07', '2025-07-11', '2025-07-13', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-07 17:06:28', '2025-07-13 13:28:45'),
(54, 1, 617, 'receita', 19, 'Hospedagem - BRÁS CLEITON DE JESUS SOUSA - UH 001', 380.00, '2025-07-07', '2025-07-11', '2025-07-13', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-07 17:24:57', '2025-07-13 13:27:37'),
(55, 1, NULL, 'receita', 19, 'Hospedagem - BRÁS CLEITON DE JESUS SOUSA - UH 001', 380.00, '2025-07-07', '2025-07-11', NULL, 'cancelado', NULL, '\nCancelado automaticamente devido ao cancelamento da reserva #618', 31, '2025-07-07 17:25:48', '2025-07-07 17:28:44'),
(56, 1, 619, 'receita', 19, 'Hospedagem - GUSTAVO LOHAM BORGES SOUSA - UH 006', 220.00, '2025-07-07', '2025-07-11', '2025-07-13', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-07 17:31:58', '2025-07-13 13:28:15'),
(57, 1, 620, 'receita', 19, 'Hospedagem - ALVARO MAURÍCIO DUARTE FREITAS - UH 002', 380.00, '2025-07-07', '2025-07-11', '2025-07-10', 'cancelado', 'PIX BRADESCO OU CARTÃO DÉBITO/CRÉDITO-TON', 'PAGOU 50% NA RESERVA NO VALOR DE R$ 190,00\n\nCANCELADO em 10/07/2025:\nDUPLO', 31, '2025-07-07 17:45:48', '2025-07-10 12:40:43'),
(58, 1, 621, 'receita', 19, 'Hospedagem - MAYARA SANTOS DUTRA - UH 002', 190.00, '2025-07-07', '2025-07-16', NULL, 'cancelado', NULL, 'CANCELADO em 10/07/2025:\nDUPLICADA', 31, '2025-07-07 19:47:21', '2025-07-10 12:32:50'),
(59, 1, 622, 'receita', 19, 'Hospedagem - MARIA DOSOCORRO MONTE SILVA (BEBEL NOIVAS TERESINA - UH 008', 570.00, '2025-07-07', '2025-07-18', '2025-07-18', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-07 19:54:58', '2025-07-18 20:23:04'),
(60, 1, 623, 'receita', 19, 'Hospedagem - FÁTIMA LETÍCIA GONÇALVES - UH 002', 1000.00, '2025-07-08', '2025-08-19', NULL, 'pendente', NULL, 'PAGOU NA RESERVA 500.00 REAIS', 31, '2025-07-08 11:31:11', '2025-07-29 22:52:38'),
(61, 1, 624, 'receita', 19, 'Hospedagem - Joel Pereira de Azevedo - UH 006', 350.00, '2025-07-08', '2025-07-09', '2025-07-10', 'cancelado', 'PIX BRADESCO OU CARTÃO DÉBITO/CRÉDITO-TON', 'CANCELADO em 10/07/2025:\nduplo', 31, '2025-07-08 18:44:45', '2025-07-10 22:05:47'),
(62, 1, 625, 'receita', 19, 'Hospedagem - Janderson Almeida Azevedo - UH 007', 350.00, '2025-07-08', '2025-07-09', '2025-07-10', 'cancelado', 'PIX BRADESCO OU CARTÃO DÉBITO/CRÉDITO-TON', 'CANCELADO em 10/07/2025:\nduplo', 31, '2025-07-08 18:47:49', '2025-07-10 22:08:22'),
(63, 1, NULL, 'despesa', 21, 'Café da manhã', 60.00, '2025-07-11', '2025-07-11', '2025-07-11', 'pago', 'PIX', '2 bolos de laranja\r\n3 pães\r\n\n\nObservação do pagamento em 11/07/2025:\nDO BRADESCO', 31, '2025-07-11 20:20:49', '2025-07-11 20:25:07'),
(64, 1, 626, 'receita', 19, 'Hospedagem - EDGAR RODRIGUES DE SOUZA - UH 005', 150.00, '2025-07-13', '2025-07-12', '2025-07-13', 'pago', 'cartão débito', NULL, 31, '2025-07-13 10:23:33', '2025-07-13 13:38:33'),
(65, 1, 627, 'receita', 19, 'Hospedagem - JEFERSON MACEDO DE SOUZA - UH 007', 150.00, '2025-07-13', '2025-07-12', '2025-07-13', 'pago', 'cartão débito', NULL, 31, '2025-07-13 10:26:29', '2025-07-13 14:03:53'),
(66, 1, NULL, 'despesa', 21, 'Café da manhã', 110.00, '2025-07-13', '2025-07-13', '2025-07-13', 'pago', 'cartão débito', '', 31, '2025-07-13 13:31:59', '2025-07-13 13:31:59'),
(67, 1, 628, 'receita', 19, 'Hospedagem - Marcos Machado  De Morais - UH 004', 150.00, '2025-07-16', '2025-07-15', '2025-07-16', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-16 10:34:46', '2025-07-16 10:54:34'),
(68, 1, NULL, 'despesa', 21, 'CAFE DA MANHA', 83.88, '2025-07-16', '2025-07-16', '2025-07-16', 'pago', 'cartão débito', 'CAFE´DE 02 HOSPEDES', 31, '2025-07-16 10:57:42', '2025-07-16 10:57:42'),
(69, 1, 629, 'receita', 19, 'Hospedagem - JAIRO PEREIRA DE AZEVEDO - UH 003', 240.00, '2025-07-16', '2025-07-18', '2025-07-19', 'pago', 'CONTA BRADESCO', '120,00 PAGO NA RESERVA', 31, '2025-07-16 11:16:18', '2025-07-19 13:50:19'),
(70, 1, 630, 'receita', 19, 'Hospedagem - AGENI MARQUES DE FREITAS - UH 004', 190.00, '2025-07-16', '2025-07-18', '2025-07-19', 'pago', 'CONTA BRADESCO', '95,00 PAGO NA RESERVA', 31, '2025-07-16 11:20:21', '2025-07-19 13:49:37'),
(71, 1, 631, 'receita', 19, 'Hospedagem - ANA LENISE MELO JÚLIO - UH 002', 380.00, '2025-07-16', '2025-07-24', '2025-07-28', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-16 15:37:21', '2025-07-28 18:52:29'),
(72, 1, 632, 'receita', 19, 'Hospedagem - MAYARA SANTOS DUTRA - UH 003', 460.00, '2025-07-17', '2025-04-18', '2025-07-29', 'pago', 'PIX', NULL, 31, '2025-07-17 10:24:08', '2025-07-29 22:47:06'),
(73, 1, 633, 'receita', 19, 'Hospedagem - SARA SILVA DOS SANTOS - UH 006', 180.00, '2025-07-17', '2025-07-21', '2025-07-23', 'pago', 'PIX', '90,00 PAGO NA RESERVA', 31, '2025-07-17 10:33:26', '2025-07-23 17:54:40'),
(74, 1, NULL, 'despesa', 21, 'Café da manhã', 35.48, '2025-07-17', '2025-07-17', '2025-07-17', 'pago', 'CONTA BRADESCO', '', 31, '2025-07-17 11:20:26', '2025-07-17 11:20:26'),
(75, 1, 634, 'receita', 19, 'Hospedagem - JARDEL DAMASCENO RODRIGUES - UH 002', 200.00, '2025-07-18', '2025-07-26', '2025-07-28', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-18 18:42:43', '2025-07-28 18:53:28'),
(76, 1, 635, 'receita', 19, 'Hospedagem - JARDEL DAMASCENO RODRIGUES - UH 004', 300.00, '2025-07-18', '2025-07-26', '2025-07-28', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-18 19:02:21', '2025-07-28 18:52:53'),
(77, 1, 636, 'receita', 19, 'Hospedagem - FAYROWS MOHAMAD EL AKHRAS - UH 002', 200.00, '2025-07-19', '2025-07-19', '2025-07-19', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-19 13:32:44', '2025-07-19 13:53:21'),
(78, 1, 637, 'receita', 19, 'Hospedagem - JARDEL DAMASCENO RODRIGUES - UH 003', 190.00, '2025-07-19', '2025-07-19', '2025-07-19', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-19 13:44:35', '2025-07-19 13:51:05'),
(79, 1, 638, 'receita', 19, 'Hospedagem - AGENI MARQUES DE FREITAS - UH 004', 190.00, '2025-07-19', '2025-07-19', '2025-07-19', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-19 13:46:14', '2025-07-19 13:50:47'),
(80, 1, 639, 'receita', 19, 'Hospedagem - RK TENDAS - UH 002', 350.00, '2025-07-19', '2025-07-21', '2025-07-23', 'pago', 'PIX', NULL, 31, '2025-07-19 20:02:19', '2025-07-23 17:54:19'),
(81, 1, 640, 'receita', 19, 'Hospedagem - RK TENDAS - UH 004', 350.00, '2025-07-19', '2025-07-21', '2025-07-23', 'pago', 'PIX', NULL, 31, '2025-07-19 20:03:44', '2025-07-23 17:54:02'),
(82, 1, 641, 'receita', 19, 'Hospedagem - RK TENDAS - UH 007', 350.00, '2025-07-19', '2025-07-21', '2025-07-23', 'pago', 'PIX', NULL, 31, '2025-07-19 20:05:01', '2025-07-23 17:53:38'),
(83, 1, 642, 'receita', 19, 'Hospedagem - SÁVIO GUIMARÃES DA CUNHA - UH 008', 240.00, '2025-07-20', '2025-07-26', '2025-07-28', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-20 15:17:33', '2025-07-28 18:51:41'),
(84, 1, NULL, 'receita', 19, 'Hospedagem - ANA KEVELLIN DE SOUSA OLIVEIRA - UH 005', 200.00, '2025-07-20', '2025-07-25', NULL, 'cancelado', NULL, 'CANCELADO em 28/07/2025:\nDESISTIU', 31, '2025-07-20 20:37:45', '2025-07-28 18:52:10'),
(85, 1, 644, 'receita', 19, 'Hospedagem - NILTON E VILMA - UH 005', 260.00, '2025-07-20', '2025-07-26', '2025-07-23', 'pago', 'TON', NULL, 31, '2025-07-20 20:44:49', '2025-07-23 17:55:57'),
(86, 1, NULL, 'receita', 19, 'Hospedagem - NILTON E VILMA - UH 005', 260.00, '2025-07-20', '2025-07-26', NULL, 'cancelado', NULL, '\nCancelado automaticamente devido ao cancelamento da reserva #645', 31, '2025-07-20 20:44:49', '2025-07-20 20:46:15'),
(87, 1, 646, 'receita', 19, 'Hospedagem - JULIANO E ÉRIKA - UH 006', 260.00, '2025-07-20', '2025-07-26', '2025-07-23', 'pago', 'TON', NULL, 31, '2025-07-20 20:48:39', '2025-07-23 17:58:57'),
(88, 1, 647, 'receita', 19, 'Hospedagem - IVANALDO E KEILA - UH 007', 260.00, '2025-07-20', '2025-07-26', '2025-07-23', 'pago', 'PIX', NULL, 31, '2025-07-20 20:51:02', '2025-07-23 17:56:49'),
(89, 1, 648, 'receita', 19, 'Hospedagem - BARROSO E CILMA - UH 001', 520.00, '2025-07-20', '2025-07-26', '2025-07-23', 'pago', 'TON', NULL, 31, '2025-07-20 20:54:08', '2025-07-23 17:55:33'),
(90, 1, 649, 'receita', 19, 'Hospedagem - JMA ENGENHARIA SOLUÇÕES EM CAD LTDA - UH 005', 190.00, '2025-07-21', '2025-07-21', '2025-07-23', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-21 15:23:52', '2025-07-23 17:53:11'),
(91, 1, 650, 'receita', 19, 'Hospedagem - MARCELLO JOSÉ BENEVIDES BORGES - UH 008', 240.00, '2025-07-21', '2025-07-21', '2025-07-23', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-21 20:06:35', '2025-07-23 17:52:46'),
(92, 1, 651, 'receita', 19, 'Hospedagem - LUANDA SOARES GRANJEIRO - UH 006', 200.00, '2025-07-22', '2025-07-22', '2025-07-23', 'pago', 'PIX', NULL, 31, '2025-07-22 13:02:08', '2025-07-23 17:57:37'),
(93, 1, 652, 'receita', 19, 'Hospedagem - JMA ENGENHARIA SOLUÇÕES EM CAD LTDA - UH 005', 190.00, '2025-07-22', '2025-07-22', '2025-07-23', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-22 14:45:09', '2025-07-23 17:57:08'),
(94, 1, 653, 'receita', 19, 'Hospedagem - WLADSON DE QUEIROZ ALCANTARA - UH 001', 540.00, '2025-07-22', '2025-07-24', '2025-07-28', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-22 18:20:25', '2025-07-28 18:51:18'),
(95, 1, 654, 'receita', 19, 'Hospedagem - DANIEL DA SILVA - UH 004', 600.00, '2025-07-23', '2025-07-23', '2025-07-24', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-23 21:42:57', '2025-07-24 20:04:16'),
(96, 1, 655, 'receita', 19, 'Hospedagem - CARLOS ANTONIO DE SOUSA VIANA - UH 006', 560.00, '2025-07-23', '2025-07-23', '2025-07-24', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-23 21:47:19', '2025-07-24 20:02:51'),
(97, 1, 656, 'receita', 19, 'Hospedagem - MARCOS DOS SANTOS FEITOSA - UH 008', 440.00, '2025-07-23', '2025-07-23', '2025-07-28', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-23 21:49:03', '2025-07-28 18:50:48'),
(98, 1, 657, 'receita', 19, 'Hospedagem - DANIEL DA SILVA - UH 004', 600.00, '2025-07-24', '2025-07-23', '2025-07-24', 'cancelado', 'CONTA BRADESCO', 'CANCELADO em 24/07/2025:\nDUPLICADO', 31, '2025-07-24 12:01:12', '2025-07-24 20:03:43'),
(99, 1, 658, 'receita', 19, 'Hospedagem - WALMIR SEVERO MAGALHAES - UH 005', 220.00, '2025-07-24', '2025-07-24', '2025-07-24', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-24 19:34:25', '2025-07-24 20:04:54'),
(100, 1, 659, 'receita', 19, 'Hospedagem - PEDRO DE SOUSA MENESES - UH 007', 150.00, '2025-07-24', '2025-07-24', '2025-07-24', 'pago', 'TON', NULL, 31, '2025-07-24 23:12:13', '2025-07-24 23:20:08'),
(101, 1, 660, 'receita', 19, 'Hospedagem - BRENO ÁVILA SANTOS - UH 007', 560.00, '2025-07-26', '2025-07-28', '2025-07-28', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-26 12:26:02', '2025-07-28 18:53:51'),
(102, 1, 661, 'receita', 19, 'Hospedagem - Ronaldo Pereira de Brito - UH 008', 200.00, '2025-07-29', '2025-07-29', '2025-07-31', 'pago', 'EM ESPÉCIE', NULL, 31, '2025-07-29 14:56:59', '2025-07-31 10:54:19'),
(103, 1, 662, 'receita', 19, 'Hospedagem - CARLOS ANDRÉ DE MORAES PEREIRA - UH 006', 200.00, '2025-07-29', '2025-09-06', NULL, 'pendente', NULL, NULL, 31, '2025-07-29 15:06:38', '2025-07-29 15:06:38'),
(104, 1, 663, 'receita', 19, 'Hospedagem - BRENO ÁVILA SANTOS - UH 007', 560.00, '2025-07-29', '2025-08-11', '2025-07-29', 'pago', 'PIX', NULL, 31, '2025-07-29 15:12:25', '2025-07-29 22:50:25'),
(105, 1, 664, 'receita', 19, 'Hospedagem - George de Castro e Silva (Pastor) - UH 003', 230.00, '2025-07-29', '2025-07-29', '2025-07-31', 'pago', 'CONTA BRADESCO', NULL, 31, '2025-07-29 22:26:49', '2025-07-31 10:48:24'),
(106, 1, 665, 'receita', 19, 'Hospedagem - ELLEN DO NASCIMENTO MONTEIRO - UH 005', 200.00, '2025-07-30', '2025-09-06', NULL, 'pendente', NULL, NULL, 31, '2025-07-30 12:41:20', '2025-07-30 12:41:20'),
(107, 1, 666, 'receita', 19, 'Hospedagem - Francisco Evaristo dos Santos (Pai Ana Maria) - UH 004', 190.00, '2025-08-01', '2025-08-09', NULL, 'pendente', NULL, NULL, 31, '2025-08-01 14:19:46', '2025-08-01 14:19:46'),
(108, 1, 667, 'receita', 19, 'Hospedagem - Francisco de Assis Vieira da Silva - UH 008', 190.00, '2025-08-01', '2025-08-09', NULL, 'pendente', NULL, NULL, 31, '2025-08-01 14:23:08', '2025-08-01 14:23:08'),
(109, 1, 668, 'receita', 19, 'Hospedagem - ANTONIO EVARISTO - UH 005', 190.00, '2025-08-01', '2025-08-09', NULL, 'pendente', NULL, NULL, 31, '2025-08-01 14:26:35', '2025-08-01 14:26:35'),
(110, 1, 669, 'receita', 19, 'Hospedagem - JORGE VIDIGAL - UH 006', 190.00, '2025-08-01', '2025-08-09', NULL, 'pendente', NULL, NULL, 31, '2025-08-01 14:30:16', '2025-08-01 14:30:16'),
(111, 1, 670, 'receita', 19, 'Hospedagem - DANIEL VIEIRA - UH 003', 190.00, '2025-08-01', '2025-08-09', NULL, 'pendente', NULL, NULL, 31, '2025-08-01 14:33:52', '2025-08-01 14:33:52'),
(112, 1, 671, 'receita', 19, 'Hospedagem - PLÍNIO MIRANDA LIMA - UH 007', 200.00, '2025-08-01', '2025-09-06', NULL, 'pendente', NULL, NULL, 31, '2025-08-01 15:30:51', '2025-08-01 15:30:51'),
(113, 1, NULL, 'despesa', 21, 'Café da manhã', 823.67, '2025-07-03', '2025-07-03', '2025-07-03', 'pago', 'PIX CONTA BRADESCO', 'referente café da manha', 31, '2025-08-03 13:01:17', '2025-08-03 13:01:17'),
(114, 1, 672, 'receita', 19, 'Hospedagem - Macedrânia Maria Moreira de Moraes  - UH 008', 380.00, '2025-08-04', '2025-08-15', NULL, 'pendente', NULL, NULL, 31, '2025-08-04 14:44:02', '2025-08-04 15:32:17'),
(115, 1, 673, 'receita', 19, 'Hospedagem - Wandemária Rodrigues da Silva - UH 005', 156.00, '2025-08-10', '2025-08-10', NULL, 'pendente', NULL, NULL, 31, '2025-08-10 23:12:01', '2025-08-10 23:12:14'),
(116, 1, NULL, 'receita', 19, 'Hospedagem - Wandemária Rodrigues da Silva - UH 004', 423.00, '2025-08-10', '2025-08-11', NULL, 'cancelado', NULL, '\nCancelado automaticamente devido ao cancelamento da reserva #674', 31, '2025-08-10 23:15:34', '2025-08-10 23:28:20');

-- --------------------------------------------------------

--
-- Estrutura para tabela `movimentacoes_caixa`
--

CREATE TABLE `movimentacoes_caixa` (
  `id` int NOT NULL,
  `caixa_id` int NOT NULL,
  `lancamento_id` int DEFAULT NULL,
  `tipo` enum('entrada','saida','suprimento','sangria') NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `data_hora` datetime NOT NULL,
  `usuario_id` int NOT NULL,
  `pousada_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `planos`
--

CREATE TABLE `planos` (
  `id` int NOT NULL,
  `nome` varchar(50) NOT NULL,
  `descricao` text,
  `preco` decimal(10,2) NOT NULL,
  `limite_usuarios` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `planos`
--

INSERT INTO `planos` (`id`, `nome`, `descricao`, `preco`, `limite_usuarios`) VALUES
(1, 'Platina', 'Até 2 usuários', 80.00, 2),
(2, 'Esmeralda', 'Até 4 usuário', 160.00, 4),
(3, 'Dourado', 'Até 6 usuários', 240.00, 6);

-- --------------------------------------------------------

--
-- Estrutura para tabela `pousadas`
--

CREATE TABLE `pousadas` (
  `id` int NOT NULL,
  `nome` varchar(100) NOT NULL,
  `cnpj` varchar(14) NOT NULL,
  `rua` varchar(100) NOT NULL,
  `numero` varchar(10) NOT NULL,
  `complemento` varchar(50) DEFAULT NULL,
  `bairro` varchar(50) NOT NULL,
  `cidade` varchar(50) NOT NULL,
  `estado` varchar(2) NOT NULL,
  `cep` varchar(10) NOT NULL,
  `pais` varchar(50) NOT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `logotipo` varchar(255) DEFAULT NULL,
  `data_cadastro` datetime DEFAULT CURRENT_TIMESTAMP,
  `cor_primaria` varchar(7) DEFAULT '#28a745' COMMENT 'Cor primária da pousada em formato HEX',
  `cor_clara` varchar(7) DEFAULT '#a8e6c1' COMMENT 'Cor clara gerada automaticamente',
  `cor_escura` varchar(7) DEFAULT '#1e7e34' COMMENT 'Cor escura gerada automaticamente',
  `cor_rgb` varchar(20) DEFAULT '40, 167, 69' COMMENT 'Valores RGB da cor primária'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `pousadas`
--

INSERT INTO `pousadas` (`id`, `nome`, `cnpj`, `rua`, `numero`, `complemento`, `bairro`, `cidade`, `estado`, `cep`, `pais`, `telefone`, `email`, `logotipo`, `data_cadastro`, `cor_primaria`, `cor_clara`, `cor_escura`, `cor_rgb`) VALUES
(0, 'Administrador', '00000000000000', 'Rua Teste', '0', NULL, 'Centro', 'Cidade', 'XX', '00000000', 'Brasil', NULL, '<EMAIL>', 'img/logos/pousada_0_logo.png', '2025-08-03 22:10:28', '#28a745', '#a8e6c1', '#1e7e34', '40, 167, 69'),
(1, 'Bom Viver Pousada LTDA', '53923211000162', 'Rua Sdo 76', '230', NULL, 'Laranjeiras', 'Viçosa do Ceará', 'CE', '62.300-000', 'Brasil', '(88) 99956-2800 ', '<EMAIL>', 'img/logos/pousada_1_logo.png', '2024-08-01 00:00:00', '#6f42c1', '#e6e3ec', '#2c1555', '111, 66, 193'),
(2, 'Pousada Teste', '12668572000122', 'Sitio Pará', 's/n', NULL, 'Zona Rural', 'Viçosa do Ceará', 'CE', '62.300-000', 'Brasil', '88999090222', '<EMAIL>', 'img/logos/pousada_2_logo.png', '2024-09-01 00:00:00', '#28a745', '#a8e6c1', '#1e7e34', '40, 167, 69');

-- --------------------------------------------------------

--
-- Estrutura para tabela `reservas`
--

CREATE TABLE `reservas` (
  `id` int NOT NULL,
  `hospede_id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `uh` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `numacomp` tinyint DEFAULT '0',
  `dataentrada` date DEFAULT NULL,
  `horaentrada` time DEFAULT NULL,
  `datasaida` date DEFAULT NULL,
  `horasaida` time DEFAULT NULL,
  `vemde` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `vaipara` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `motivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `transporte` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `acompanhantes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `valor` decimal(10,2) DEFAULT NULL,
  `observacao` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `reservas`
--

INSERT INTO `reservas` (`id`, `hospede_id`, `pousada_id`, `uh`, `numacomp`, `dataentrada`, `horaentrada`, `datasaida`, `horasaida`, `vemde`, `vaipara`, `motivo`, `transporte`, `acompanhantes`, `valor`, `observacao`) VALUES
(1, 1, 1, '004', 3, '2024-08-23', '13:00:00', '2024-08-25', '12:00:00', 'Capitão De  Campos', 'Congresso Testemunha de Jeová/2024', 'Congresso', 'Automovel', '(Filho)       -  Breno Henrique Rodrigues da Silva.\r\n(Filha)       -  Rayanne Rodrigues da Silva.\r\n(Esposo)   -  Adriano Almeida Mouzinho\r\n\r\nCONGRESSO TESTEMUNHAS DE JEOVÁ\r\n', 500.00, ''),
(2, 2, 1, '005', 1, '2024-08-22', '14:00:00', '2024-08-25', '12:00:00', 'Capitão De  Campos', 'Congresso Testemunha de Jeová/2024', 'Congresso', 'Outro', 'Artur Vinícios Martins Oliveira\r\n', 360.00, NULL),
(3, 3, 1, '002', 1, '2024-08-23', '14:00:00', '2024-08-25', '12:00:00', 'Piripiri', 'Congresso Testemunha de Jeová/2024', 'Congresso', 'Outro', 'Emiliana dos Santos Sousa Silva (esposa)', 320.00, NULL),
(4, 4, 1, '008', 1, '2024-08-23', '14:00:00', '2024-08-25', '12:00:00', 'Capitão De  Campos', 'Congresso Testemunha de Jeová/2024', 'Congresso', 'Outro', '', 300.00, NULL),
(5, 5, 1, '001', 3, '2024-08-23', '16:00:00', '2024-08-25', '11:00:00', 'Pedro II', 'Congresso (Testemunha de Jeová/2024)', 'Congresso', 'Outro', 'Agatha Cristini Soares Cruz (15 anos) \r\nAlice Mariah Soares Cruz ( 11 anos )\r\n\r\nAmora Soares Cruz (3 anos )', 500.00, NULL),
(6, 6, 1, '003', 2, '2024-08-23', '14:00:00', '2024-08-25', '12:00:00', 'Piripiri', 'Congresso Testemunha de Jeová/2024', 'Congresso', 'Automovel', 'Selma Andrade (esposa)\r\nYago Andrade   (filho)\r\nCONGRESSO TESTEMUNHAS DE JEOVÁ 2024', 500.00, NULL),
(7, 7, 1, '006', 3, '2024-08-22', '14:00:00', '2024-08-25', '12:00:00', 'Capitão De  Campos', 'Congresso Testemunha de Jeová/2024', 'Congresso', 'Outro', 'Larissa Carolini Brasil (esposa)', 510.00, NULL),
(8, 8, 1, '002', 1, '2024-08-10', '14:00:00', '2024-08-11', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Outro', 'Automovel', 'Neide (esposa)', 180.00, NULL),
(9, 9, 1, '001', 0, '2024-04-11', '14:00:00', '2024-04-14', '12:00:00', 'Pedra Branca - Ce', 'Vicosa do Ceará - CE', 'Negocios', 'Onibus', 'Festival Herança Nativa (Promovido pelo SESC\r\n(três diarias)', 270.00, NULL),
(10, 10, 1, '002', 1, '2024-04-11', '14:00:00', '2024-04-14', '12:00:00', 'Fortaleza', 'Herança Nativa', 'Negocios', 'Onibus', 'herança nativa', 270.00, NULL),
(11, 11, 1, '003', 1, '2024-04-10', '14:00:00', '2024-04-14', '12:00:00', 'Ibiapina-Ce', 'Vicosa do Ceará - CE', 'Negocios', 'Onibus', 'SESC IBIAPINA  -  HERANÇA NATIVA', 270.00, NULL),
(12, 12, 1, '004', 1, '2024-04-11', '14:00:00', '2024-04-14', '12:00:00', 'Fortaleza', 'Herança Nativa', 'Negocios', 'Onibus', 'HERANÇA NATIVA', 270.00, NULL),
(13, 13, 1, '005', 0, '2024-04-11', '14:00:00', '2024-04-14', '12:00:00', '', 'Herança Nativa', 'Congresso', 'Outro', '', 270.00, NULL),
(14, 14, 1, '006', 0, '2024-04-11', '14:00:00', '2024-04-14', '12:00:00', 'Fortaleza', 'Herança Nativa', 'Negocios', 'Onibus', 'HERANÇA NATIVA', 270.00, NULL),
(15, 15, 1, '007', 0, '2024-04-10', '14:00:00', '2024-04-14', '12:00:00', 'Fortaleza', 'Herança Nativa', 'Congresso', 'Outro', '', 100.00, NULL),
(16, 16, 1, '002', 1, '2024-08-16', '14:00:00', '2024-08-18', '12:00:00', 'Guaraciaba do norte', 'Congresso Testemunha de Jeová', 'Congresso', 'Outro', 'Esposa: Maria Erinete Alves de Sousa', 180.00, NULL),
(17, 17, 1, '001', 3, '2024-08-02', '14:00:00', '2024-08-03', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Rosângela Costa\r\nFrancisca das Chagas Ribeiro\r\nIan Carlos Costa', 270.00, NULL),
(18, 18, 1, '003', 1, '2024-08-16', '11:30:00', '2024-08-18', '12:00:00', 'Ubajara', ' Viçosa do Ceará', 'Ferias', 'Automovel', '(Esposo) - José Juaci Costa dos Santos', 360.00, NULL),
(19, 19, 1, '001', 1, '2024-08-16', '16:00:00', '2024-08-17', '12:00:00', 'Cruz- CE', 'Guaraciaba do Norte-Ce', 'Ferias', 'Automovel', 'esposa: Flávia Lima\r\nfilho criança', 180.00, NULL),
(20, 20, 1, '004', 2, '2024-08-17', '14:00:00', '2024-08-18', '12:00:00', '', '', '', '', 'esposa: Doutora Conceição \r\nfilha: Lívia Maria\r\n', 180.00, NULL),
(21, 21, 1, '004', 4, '2024-08-16', '17:00:00', '2024-08-17', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', 'Jozielma Cardoso - esposa\r\n(Filho)     Joao Paulo Cardoso\r\n(Filha)     Clara Elys Cardoso\r\n(Filha)     Vitoria Cardoso\r\n', 250.00, NULL),
(22, 22, 1, '005', 1, '2024-07-26', '13:00:00', '2024-07-27', '12:00:00', 'Campina Grande-PB', 'vicosa do ceara', 'Negocios', 'Automovel', 'PERNOITE\r\n(Esposa) Maria Auxiliadora', 120.00, NULL),
(23, 23, 1, '004', 1, '2024-07-26', '13:00:00', '2024-07-27', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Nayra Costa -  Festival MI/2024', 200.00, NULL),
(24, 24, 1, '006', 1, '2024-07-26', '13:00:00', '2024-07-27', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', '(Maquiadores)\r\nFestival de música', 180.00, NULL),
(25, 25, 1, '004', 2, '2024-07-21', '13:00:00', '2024-07-22', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', '(Esposo)  -  Douglas Sousa\r\n(Filha)      -   Débora Nobre Duarte', 250.00, NULL),
(26, 26, 1, '001', 3, '2024-07-19', '13:00:00', '2024-07-22', '12:00:00', 'São Luis-Ma', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa)  -  Carol Magalhaes\r\n(Filha)      -   Lara Mendes (8 anos)\r\n(Filha)      -   Lia Mendes    (2 anos)', 540.00, NULL),
(27, 27, 1, '002', 1, '2024-07-26', '13:00:00', '2024-07-27', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Banda Forró Briseira\r\nFESTIVAL DE MÚSICA DA IBIAPABA', 200.00, NULL),
(28, 28, 1, '001', 1, '2024-07-26', '13:00:00', '2024-07-27', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Danilo Ramalho\r\nFESTIVAL DE MÚSICA DA IBIAPABA/2024', 200.00, NULL),
(29, 29, 1, '002', 1, '2024-07-19', '13:00:00', '2024-07-20', '12:00:00', 'São Luis-MA', 'vicosa do ceara', 'Outro', 'Automovel', 'Mariana Cardoso', 180.00, NULL),
(30, 30, 1, '007', 2, '2024-07-19', '13:00:00', '2024-07-21', '12:00:00', 'Campo - Maior-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Angelica e (Um neto)', 360.00, NULL),
(31, 31, 1, '006', 2, '2024-07-19', '13:00:00', '2024-07-21', '12:00:00', 'Campo-Maior', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa)   -   Maralima de Castro da Silva\r\n(Filho)       -   Arthur Abner de Castro Brandão', 360.00, NULL),
(32, 32, 1, '001', 2, '2024-07-05', '13:00:00', '2024-07-06', '12:00:00', 'Teresina-Pi', 'Fortaleza', 'Outro', 'Automovel', '(Esposa)  Raiolinda Melo\r\nCésar Campelo\r\n\r\nPERNOITE', 180.00, NULL),
(33, 33, 1, '007', 1, '2024-07-04', '13:00:00', '2024-07-05', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', '(Esposa)  -  Antonia Mesquita do Amaral\r\nPERNOITE', 120.00, NULL),
(34, 34, 1, '002', 1, '2024-06-15', '13:00:00', '2024-06-16', '12:00:00', 'Tianguá', 'Irmãos em Cristo', 'Congresso', 'Outro', 'Felizardo Ramos Alves', 200.00, NULL),
(35, 35, 1, '008', 1, '2024-06-01', '13:00:00', '2024-06-02', '12:00:00', 'Sobral - Ce', 'vicosa do ceara', 'Outro', 'Automovel', ' Ana Kélvia Alves', 150.00, NULL),
(36, 36, 1, '003', 4, '2024-06-01', '13:00:00', '2024-06-02', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'José da Cruz Ramos Filho\r\nGeorge Holanda Alencar\r\nMarcos Victor da Silva \r\nRodrigo Botti Guimarães', 400.00, NULL),
(37, 37, 1, '003', 5, '2024-05-31', '13:00:00', '2024-06-01', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Tauí Castro\r\nSebastião Lucas de Freitas\r\nCarlos Patriolino Filho\r\nJosé da Cruz Ramos Filho\r\nMonalyza Cavalcante Pontes\r\nJosé\r\nMonal', 500.00, NULL),
(38, 38, 1, '003', 4, '2024-05-30', '13:00:00', '2024-05-31', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Paulo David Rodrigues Barbosa\r\nDiego Emanuel do Nascimento\r\nRegislane Carmo da Silva\r\nMarinaldo Carmo da Silva', 500.00, NULL),
(39, 39, 1, '007', 1, '2024-05-30', '13:00:00', '2024-06-02', '12:00:00', 'Parnaíba-Pi', 'Festival Mel e Cachaça 2024', 'Ferias', 'Automovel', 'Sherelyn Nicia', 1000.00, NULL),
(40, 40, 1, '006', 2, '2024-05-30', '13:00:00', '2024-06-02', '12:00:00', 'Fortaleza', 'Festival Mel e Cachaça 2024', 'Ferias', 'Automovel', 'Rozina  Maria Lessa Rocha\r\nDionne Peixoto Mota\r\nFESTIVAL MEL E CACHAÇA 2024', 1450.00, NULL),
(41, 41, 1, '004', 1, '2024-05-30', '13:00:00', '2024-06-02', '12:00:00', 'Fortaleza', 'Viçosa do Ceará-Ce', 'Ferias', 'Outro', '(Esposo)  -  Ronaldo Figueira Cruz\r\nFESTIVAL MEL E CACHAÇA DE 2024', 1000.00, NULL),
(42, 42, 1, '002', 1, '2024-05-30', '13:00:00', '2024-05-02', '12:00:00', 'Parnaíba-Pi', 'Festival Mel e Cachaça 2024', 'Ferias', 'Automovel', 'Josélia da Costa', 1000.00, ''),
(43, 43, 1, '005', 1, '2024-05-30', '13:00:00', '2024-06-02', '12:00:00', 'Fortaleza', 'Festival Mel e Cachaça 2024', 'Ferias', 'Automovel', 'Nilce Clicia Maia Queiroz', 900.00, 'FESTIVAL MEL E CACHAÇA 2024'),
(44, 44, 1, '007', 1, '2024-05-13', '13:00:00', '2024-05-14', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Elaine Maria da Silva Lima', 120.00, NULL),
(45, 45, 1, '001', 1, '2024-05-03', '13:00:00', '2024-05-05', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', 'David de Almeida', 340.00, NULL),
(46, 46, 1, '006', 1, '2024-04-26', '13:00:00', '2024-04-28', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', 'Renata Lima Viana', 440.00, NULL),
(47, 47, 1, '002', 1, '2024-04-26', '13:00:00', '2024-04-27', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'nova reserva para 14.12.2024 a 16.12,2024 vl duas diarias 360.00\r\nInacio Carvalho Rocha e Expedita Michelly\r\nsuite 002', 170.00, NULL),
(48, 48, 1, '002', 1, '2024-03-28', '13:00:00', '2024-03-31', '12:00:00', 'Sobral=Ce', 'Semana Santa', 'Ferias', 'Automovel', 'Helan Chagas', 500.00, NULL),
(49, 49, 1, '005', 1, '2024-03-29', '13:00:00', '2024-03-30', '12:00:00', 'Fortaleza', 'Semana Santa', 'Ferias', 'Automovel', 'Maria dos Santos Araújo- esposa', 250.00, NULL),
(50, 50, 1, '002', 1, '2024-03-30', '13:00:00', '2024-03-31', '12:00:00', 'Fortaleza', 'Semana Santa', 'Ferias', 'Automovel', ' Sabrina de Paula da Silva', 300.00, NULL),
(51, 51, 1, '002', 1, '2024-03-29', '13:00:00', '2024-03-30', '12:00:00', 'Parnaíba-Pi', 'Semana Santa/Viçosa do Ceará', 'Ferias', 'Automovel', 'Sabrina Araújo', 250.00, NULL),
(52, 52, 1, '001', 3, '2024-03-30', '13:00:00', '2024-03-31', '12:00:00', '', ' Abertura Festival Mel e Cachaça 2024', 'Ferias', 'Outro', 'Dayane, Jhonatan e Larissa', 500.00, NULL),
(53, 53, 1, '003', 1, '2024-03-28', '13:00:00', '2024-04-01', '12:00:00', '', 'Semana Santa', 'Ferias', 'Automovel', 'Silvia Cristina ', 680.00, NULL),
(54, 54, 1, '004', 1, '2024-03-30', '13:00:00', '2024-03-31', '12:00:00', '', ' Abertura Festival Mel e Cachaça 2024', 'Ferias', 'Automovel', 'Francisca Loana Melo ', 180.00, NULL),
(55, 55, 1, '005', 1, '2024-03-30', '13:00:00', '2024-03-31', '12:00:00', '', 'Abertura Festival Mel e Cachaça 2024', 'Ferias', 'Automovel', 'Cézar Bezerra', 180.00, NULL),
(56, 56, 1, '006', 1, '2024-03-29', '13:00:00', '2024-03-31', '12:00:00', '', 'Semana Santa', 'Ferias', 'Automovel', 'Sabrina de Oliveira Vasconcelos', 350.00, NULL),
(57, 57, 1, '007', 0, '2024-03-28', '13:00:00', '2024-03-31', '12:00:00', '', 'Semana Santa', 'Ferias', 'Automovel', '', 300.00, NULL),
(59, 59, 1, '001', 1, '2024-02-21', '13:00:00', '2024-02-25', '12:00:00', 'Jijoca', 'vicosa do ceara', 'Outro', 'Automovel', 'Ronier Garcia ( BYCON )', 670.00, NULL),
(60, 60, 1, '004', 0, '2024-02-19', '13:00:00', '2024-02-21', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Negocios', 'Automovel', '', 270.00, NULL),
(61, 61, 1, '006', 1, '2024-08-22', '13:00:00', '2024-08-25', '12:00:00', 'Piripiri', 'Congresso Testemunha de Jeová/2024', 'Congresso', 'Outro', 'Francisca Oliveira de Sousa (esposa)', 510.00, NULL),
(62, 62, 1, '008', 2, '2024-05-31', '13:00:00', '2024-06-02', '12:00:00', 'Sobral-Ce', 'Festival Mel e Cachaça 2024', 'Ferias', 'Automovel', 'Anne Caroliny Soares Siqueira\r\nFelipe Martins', 900.00, NULL),
(63, 63, 1, '001', 0, '2024-06-04', '13:00:00', '2024-06-05', '12:00:00', 'sobral ce', 'vicosa do ceara', 'Negocios', 'Automovel', '    ', 80.00, NULL),
(64, 64, 1, '005', 1, '2024-02-13', '13:00:00', '2024-02-14', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Outro', 'Jerônimo José da S. Araújo (Empresario) Amigos do Pastor George', 170.00, NULL),
(65, 65, 1, '002', 1, '2024-02-17', '13:00:00', '2024-02-18', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa)     Aulecír Silva S. Barbosa', 140.00, NULL),
(66, 66, 1, '007', 2, '2024-02-12', '13:00:00', '2024-02-14', '12:00:00', 'Piripiri', 'vicosa do ceara', 'Outro', 'Automovel', 'Dourival Ferreira Brandão\r\nArthur Abner de Castro Brandão', 400.00, NULL),
(67, 67, 1, '006', 3, '2024-02-12', '13:00:00', '2024-02-13', '12:00:00', 'Teresina-Pi', 'Carnaval 2024/Viçosa do Ceará', 'Ferias', 'Automovel', 'Leonardo Poretela Leite\r\nJandira de Moura C. Passos\r\nBenjamin Passos Leite', 250.00, NULL),
(68, 68, 1, '003', 1, '2024-02-12', '13:00:00', '2024-02-13', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Diana Galeno Castro e Silva', 180.00, NULL),
(69, 69, 1, '001', 2, '2024-02-12', '13:00:00', '2024-02-13', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Outro', 'Automovel', 'George de Castro Filho\r\nAna Beatriz Castro', 205.00, NULL),
(70, 70, 1, '006', 3, '2024-02-11', '13:00:00', '2024-02-12', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Jerione Soares Maia\r\nAna Júlia\r\nIsabelly ', 200.00, NULL),
(71, 71, 1, '002', 1, '2024-02-11', '13:00:00', '2024-02-14', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Leila Maria de Oliveira', 460.00, NULL),
(72, 72, 1, '006', 3, '2024-02-11', '13:00:00', '2024-02-12', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', 'Johnny Alison Chaves\r\ncriança 1 \r\ncriança 2', 170.00, NULL),
(73, 73, 1, '004', 1, '2024-02-11', '13:00:00', '2024-02-14', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Outro', 'Automovel', 'Instaley Nascimento Gomes', 500.00, NULL),
(74, 74, 1, '001', 3, '2024-02-10', '13:00:00', '2024-02-11', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Clara Rosária Lacerda Bonfim\r\nYure Emanuel B. C. de Carvalho\r\nPedro Gael B.C. de Carvalho', 200.00, NULL),
(75, 75, 1, '004', 3, '2024-02-02', '13:00:00', '2024-02-03', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Magual Nunes Maia\r\nHeloísa Maia de Melo\r\nJoão Pedro Maia de Melo', 180.00, NULL),
(76, 76, 1, '002', 1, '2024-09-14', '13:00:00', '2024-09-15', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Isaac Sousa Ponte', 170.00, NULL),
(77, 77, 1, '002', 1, '2024-01-27', '13:00:00', '2024-01-29', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Outro', 'Automovel', 'Rosangela Matos Oliveira Marques', 340.00, NULL),
(78, 78, 1, '007', 1, '2024-01-26', '13:00:00', '2024-01-27', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa)  -  Layna Oliveira', 120.00, NULL),
(79, 79, 1, '001', 2, '2024-02-17', '13:00:00', '2024-02-18', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Outro', 'Automovel', 'Gê Rodrigues\r\nBreno Medeiros', 170.00, NULL),
(80, 80, 1, '002', 1, '2024-01-24', '13:00:00', '2024-01-25', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Ana Paula Fidelis', 150.00, NULL),
(81, 81, 1, '001', 3, '2024-01-24', '13:00:00', '2024-01-25', '12:00:00', 'Parauapebas-Pa', 'PIOCERÁ 2024', 'Outro', 'Automovel', 'Miqueías /Glauco e Rafael\r\nPARTICIPANTES DO PIOCERÁ\r\n\r\n', 440.00, NULL),
(82, 82, 1, '001', 4, '2024-01-24', '13:00:00', '2024-01-25', '12:00:00', 'Pará', 'PIOCERÁ 2024 ', 'Negocios', 'Automovel', 'Heverton Rodrigo\r\nCelis Pimentel\r\nHernan\r\nHalrivan Fernandes', 550.00, NULL),
(83, 83, 1, '003', 2, '2024-01-24', '13:00:00', '2024-01-25', '12:00:00', 'Parauapebas-Pa', 'Cerapió/ Piocerá', '', '', 'Alana Meneses\r\nLetícia Vidal', 300.00, NULL),
(84, 84, 1, '007', 0, '2024-01-23', '13:00:00', '2024-01-25', '12:00:00', '', 'Herança Nativa', 'Negocios', 'Automovel', '', 180.00, NULL),
(85, 85, 1, '007', 1, '2024-01-13', '13:00:00', '2024-01-14', '12:00:00', 'Capitão De  Campos', 'vicosa do ceara', 'Ferias', 'Automovel', 'Maria Elisa Severiano Barbosa', 120.00, NULL),
(86, 86, 1, '003', 1, '2024-01-13', '13:00:00', '2024-01-14', '12:00:00', '', '', 'Ferias', 'Automovel', 'Tertuliano Ramos Goes Noleto', 500.00, NULL),
(87, 87, 1, '007', 1, '2023-12-30', '13:00:00', '2024-01-01', '12:00:00', 'Aquiraz-Ce', 'vicosa do ceara', 'Outro', 'Automovel', 'Francisca Márcia Vidal da Silva (Reveilhon 2024)', 420.00, NULL),
(88, 88, 1, '001', 1, '2024-09-18', '13:00:00', '2024-09-19', '12:00:00', '', '', 'Negocios', 'Automovel', 'Kaike Victor Gonçales Freitas', 100.00, NULL),
(89, 89, 1, '004', 4, '2024-09-18', '13:00:00', '2024-09-19', '12:00:00', '', '', 'Negocios', 'Automovel', 'Leidiene, Aldenoura, Josineide, Luiza Sofia', 280.00, NULL),
(90, 90, 1, '001', 1, '2024-09-19', '13:00:00', '2024-09-20', '12:00:00', 'Baturité', 'Barreirinhas/MA', '', 'Automovel', 'Maria de Fátima Farias', 170.00, NULL),
(91, 91, 1, '002', 1, '2024-09-19', '13:00:00', '2024-09-20', '12:00:00', '', '', 'Negocios', 'Automovel', 'Antonio Ednardo Oliveira Andrade', 170.00, NULL),
(92, 92, 1, '005', 1, '2023-12-30', '13:00:00', '2024-01-01', '12:00:00', 'São Luis-Ma', 'Ano novo em Viçosa do Ceará', 'Ferias', 'Automovel', 'Isadora Ilana Costa Melo', 440.00, NULL),
(93, 93, 1, '001', 3, '2023-12-29', '13:00:00', '2023-12-30', '12:00:00', '', '', 'Ferias', 'Automovel', 'Pedro Caetano\r\nPedro Ferraz\r\nLandeliny', 300.00, NULL),
(94, 94, 1, '006', 3, '2023-12-15', '13:00:00', '2023-12-16', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', '', 'Frantony dos Santos\r\nFrancimar dos Santos\r\nAna Lavínia dos Santos', 350.00, NULL),
(95, 95, 1, '002', 0, '2023-12-17', '13:00:00', '2023-12-17', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', '', '', 170.00, NULL),
(96, 96, 1, '002', 1, '2024-09-21', '13:00:00', '2024-09-22', '12:00:00', '', '', '', '', 'Danielle de Alcantara Vasconcelos Manso', 180.00, NULL),
(97, 97, 1, '002', 1, '2024-12-23', '13:00:00', '2024-12-24', '12:00:00', 'Fortaleza', 'Natal', 'Ferias', 'Automovel', 'Raquel Lange', 120.00, NULL),
(98, 98, 1, '002', 4, '2024-12-28', '13:00:00', '2024-12-29', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', 'Ant. Ferreira\r\nM. Lucia Silva\r\nJoão Gabriel Cunha\r\nLuan Victor da Costa', 340.00, NULL),
(99, 99, 1, '007', 1, '2023-12-29', '13:00:00', '2023-12-30', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Cibele Soares', 120.00, NULL),
(100, 100, 1, '004', 4, '2023-12-30', '13:00:00', '2023-12-31', '12:00:00', 'Sobral - Ce', 'vicosa do ceara', 'Ferias', '', 'Johnny Alison Chaves\r\nBaby\r\nCriança \r\nDona Fátima', 210.00, NULL),
(101, 101, 1, '007', 1, '2023-12-30', '13:00:00', '2024-01-01', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', 'Fernando Gomes', 540.00, NULL),
(102, 102, 1, '006', 2, '2023-12-30', '13:00:00', '2024-01-01', '12:00:00', 'São Luis-MA', 'vicosa do ceara', 'Outro', 'Automovel', 'Ângela Gabriela Costa Moura\r\nAntônio Rodrigues Melo', 660.00, NULL),
(103, 103, 1, '004', 1, '2023-12-31', '13:00:00', '2024-01-01', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Gilmara Ellen de Sousa Alencar (Reveilhon/2024)', 260.00, NULL),
(104, 104, 1, '003', 3, '2023-12-31', '13:00:00', '2024-01-01', '12:00:00', '', 'Ano novo', 'Ferias', '', 'F. Luciane Linhares Azevedo\r\nVictor Linhares Azevedo\r\nAna Vitória Linhares Azevedo', 400.00, NULL),
(105, 105, 1, '002', 3, '2023-12-31', '13:00:00', '2023-01-01', '12:00:00', '', '', '', '', 'Janielle Maria dos Santos\r\nJamille Maria Vitória\r\nJanine Maria das Graças', 450.00, NULL),
(106, 106, 1, '005', 0, '0024-09-21', '13:00:00', '0024-09-23', '12:00:00', 'Juazeiro', 'Granja', 'Outro', 'Automovel', '', 150.00, NULL),
(107, 107, 1, '003', 2, '2023-02-18', '13:00:00', '2023-02-20', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposo)  Francisco Daniel Vieira De Oliveira\r\n(Filha)     Manuela Santos Vieira Oliveira (2 anos)\r\nHosp. 28/03/24 a 31/03/24 (03 diárias) $ 480,00', 400.00, NULL),
(108, 108, 1, '004', 4, '2024-11-16', '13:00:00', '2024-11-17', '12:00:00', 'Fortaleza', 'Vicosa do ceara', 'Outro', 'Automovel', '(esposo) Vandeirys Rocha Cardoso\r\n(Filho)     Joao Paulo Cardoso\r\n(Filha)     Clara Elys Cardoso\r\n(Filha)     Vitoria Cardoso', 300.00, NULL),
(109, 109, 1, '001', 4, '2024-11-07', '11:00:00', '2024-11-08', '19:00:00', '', 'Fortaleza', 'Ferias', 'Automovel', 'Maiara esposa\r\n3 filhas\r\nKésia Honorato de Sousa\r\nLorrane Honorato de Sousa\r\nClara Evelin Honorato de Sousa\r\nAna Lívia Vieira dos Santos', 220.00, NULL),
(111, 111, 1, '002', 0, '2024-11-21', '13:00:00', '2024-11-23', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Cliente pagou 50% na reserva no valor de 120,00\r\nFica o restante no valor de $ 120,00 no check-in', 240.00, NULL),
(112, 112, 1, '001', 2, '2004-12-21', '13:00:00', '2004-12-22', '12:00:00', 'Ipú-Ce', 'vicosa do ceara', 'Outro', 'Automovel', 'Mae - Maria Francelina Martins Aragão\r\nFilho - Davi Lucca Martins Aragão Sampaio\r\n\r\nObs: Pagou 50% da Reserva no valor de 110.00 reais', 220.00, 'Batizado na Igreja do Céu'),
(113, 113, 1, '003', 0, '2024-11-21', '13:00:00', '2024-11-23', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'obs.. Pagou 50% na reserva no valor de $120,00\r\n         Restante na hospedagem', 240.00, NULL),
(114, 114, 1, '005', 0, '2024-11-22', '13:00:00', '2024-11-23', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Obs: Pagou reserva de 50% no valor de 60.00\r\nRestante na hospedagem', 120.00, NULL),
(115, 115, 1, '006', 0, '2024-11-22', '13:00:00', '2024-11-23', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Obs: Pagou valor total de uma diária (QUITADO)', 120.00, NULL),
(116, 116, 1, '007', 0, '2024-11-22', '13:00:00', '2024-11-23', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Obs   Pagou 50% da reserva no valor de 60.00 reais\r\nRestante na Hospedagem', 120.00, NULL),
(117, 117, 1, '004', 3, '2024-11-16', '13:00:00', '2024-11-17', '12:00:00', 'Campo-Maior', 'vicosa do ceara', 'Outro', 'Automovel', 'Adriana Silva    (amiga)\r\nFabiana Silva    (amiga)\r\nMaria do Desterro (amiga)\r\nObs: Pagamento total no chek-in', 340.00, NULL),
(118, 118, 1, '002', 2, '2024-12-21', '13:00:00', '2024-12-22', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Esposa      -     Marcia\r\nFilha          -     Bruna', 200.00, NULL),
(119, 119, 1, '001', 3, '2024-11-21', '13:00:00', '2024-11-22', '12:00:00', 'Juazeiro do Norte-Ce', 'vicosa do ceara', 'Outro', 'Automovel', 'Genuza Ferreira de Sousa               (Babá)\r\nMaria Helena Mendes Quirino          (Filha)\r\nGeovana de Souza Silva                  (Amiga)\r\nOBS:       Cliente pagou valor total da reserva ($ 300,00)\r\n-\r\n-\r\n\r\n', 300.00, 'Batizado no Sitio Delgada'),
(120, 120, 1, '005', 1, '2024-12-29', '13:00:00', '2025-01-01', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Outro', 'Automovel', '(Esposo)  -  Valtevan Rocha dos Santos\r\n\r\nObs.  Entrada da reserva 50% R$375,00 \r\nPacote Réveillon 2025 (3 diárias) ', 750.00, NULL),
(121, 121, 1, '005', 1, '2024-11-16', '13:00:00', '2024-11-17', '12:00:00', 'Parnaiba-Pi', 'Vicosa do Ceará - CE', 'Outro', 'Outro', '(Esposa) --Milena Silvestre Souza Braga\r\n\r\nCliente pagou 50% na Reserva no valor de $ 90.00\r\nRestante no Check-in\r\n', 180.00, NULL),
(122, 122, 1, '001', 3, '2024-11-14', '13:00:00', '2024-11-16', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Outro', 'Automovel', '(Esposa)  -  Laudyane Miranda Melo\r\n(Filha)      -  Maria Helena Melo Brandão\r\n(Filho)      -  Lucas Emerson Melo Brandão\r\n                -  OBS. Cliente pagou 50% reserva no valor de $ 230,00\r\n                -  Restante na Hospedagem.', 460.00, NULL),
(123, 123, 1, '006', 1, '2024-11-16', '13:00:00', '2024-11-17', '12:00:00', 'Paulista - Pe', 'vicosa do ceara', 'Negocios', 'Automovel', '(Esposa)  -  Lourdes de Almeida\r\nOBS. Pagamento de 50% da reserva no valor de $ 85,00\r\n(À CONFIRMAR)\r\n                 ', 170.00, NULL),
(124, 124, 1, '002', 1, '2024-11-16', '13:00:00', '2024-11-17', '12:00:00', 'Ipu-Ce', 'vicosa do ceara', 'Outro', 'Outro', '(Esposa)   Maria Eduarda Farias de Aguiar Lobo\r\n\r\nOBS: Pagamento 50 % efetuado', 180.00, NULL),
(125, 125, 1, '001', 1, '2024-11-16', '13:00:00', '2024-11-17', '12:00:00', 'Caraúbas do Piauí', 'vicosa do ceara', 'Negocios', 'Automovel', 'A trabalho - Ivanildo Felipe de Araújo Machado\r\n\r\n*********** -  Pagamento ', 170.00, NULL),
(126, 126, 1, '003', 3, '2024-11-16', '13:00:00', '2024-11-17', '12:00:00', 'Belém-Pa', 'Vicosa do Ceará - CE', 'Ferias', 'Automovel', '(Esposa) - Vanice Ribeiro Cordeiro\r\n(Filho)     -  Davi Ribeiro\r\n(Filha)     -  Elisa Ribeiro', 200.00, NULL),
(127, 127, 1, '001', 18, '2024-06-15', '13:00:00', '2024-06-16', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Onibus', 'Márcio/Cássia/Janaína/Jeane Iago/Dulce/Maria Íngride/Pedro/Edilene/\r\nIllana Batista/Francisca Batista/Maria de Fátima/Vera Lúcia/Maria da \r\nConceição/Leonízia / Suzenír e Aloísio', 850.00, NULL),
(128, 128, 1, '004', 0, '2024-11-19', '13:00:00', '2024-11-22', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Negocios', 'Automovel', 'Suíte Single', 300.00, NULL),
(129, 129, 1, '001', 0, '2024-11-19', '13:00:00', '2024-11-22', '12:00:00', 'Cocal da Estação-Pi', 'vicosa do ceara', 'Negocios', 'Automovel', 'Suite single\r\nRepresentante da MAGOLD', 300.00, NULL),
(130, 130, 1, '001', 0, '2022-04-30', '13:00:00', '2022-05-01', '12:00:00', 'Parnaíba-Pi', 'Abertura Festival em Viçosa do Ceará', 'Negocios', 'Onibus', 'OBS: Minha primeira Hospedagem\r\nPeríodo de Abertura do Festival Mel, Chorinho e Cachaça de 2022\r\nVIÇOSA DO CEARÁ', 80.00, NULL),
(131, 131, 1, '002', 0, '2022-04-30', '13:00:00', '2022-05-01', '12:00:00', 'Parnaíba-Pi', 'Vicosa do Ceará - CE', 'Outro', 'Automovel', 'OBS: MEU SEGUNDO HÓSPEDE\r\nVEIO PRA ABERTURA DO FESTIVAL MEL , CHORINHO E CACHAÇA', 80.00, NULL),
(132, 132, 1, '001', 16, '2022-06-15', '13:00:00', '2022-06-18', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'João César Silveira(Professor) Adriana Pontes(Contadora) Priscila\r\nAlburquerque e Daiã Gouveia (Músico) Alexandre Tande e mais 12\r\npessoas.                          (Aluguel da casa pra o Festival/2022)', 3300.00, NULL),
(133, 133, 1, '004', 1, '2023-04-06', '13:00:00', '2023-04-09', '12:00:00', 'São Luís-MA', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa) -  Daniele Gomes Camargo (Semana Santa)', 500.00, NULL),
(134, 134, 1, '001', 2, '0000-00-00', '13:00:00', '2023-01-08', '12:00:00', '', '', '', '', 'Maria Onaira G. Ferreira\r\ncriança', 0.00, NULL),
(135, 135, 1, '001', 0, '2022-09-09', '13:00:00', '2022-09-10', '12:00:00', '', '', '', '', '(Chaguinha civil)', 30.00, NULL),
(136, 136, 1, '002', 0, '2022-10-01', '13:00:00', '2022-10-02', '12:00:00', '', '', '', '', 'João Rodrigues N. Filho\r\nReginaldo José da Silva', 0.00, NULL),
(137, 137, 1, '001', 0, '0000-00-00', '13:00:00', '0000-00-00', '12:00:00', '', '', '', '', '', 90.00, NULL),
(138, 138, 1, '003', 2, '2024-11-23', '13:00:00', '2024-11-24', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Ana Maria Araújo dos Santos Silva (Esposa)\r\nJosé Victor dos Santos Gomes (Sobrinho)', 550.00, NULL),
(139, 139, 1, '001', 0, '2022-08-04', '13:00:00', '2022-08-07', '12:00:00', '', '', '', '', '', 0.00, NULL),
(140, 140, 1, '001', 0, '2022-08-04', '13:00:00', '0000-00-00', '12:00:00', '', '', '', '', '', 50.00, NULL),
(141, 141, 1, '001', 1, '2022-07-19', '13:00:00', '2022-07-21', '12:00:00', '', '', '', '', '', 0.00, NULL),
(142, 142, 1, '001', 0, '2022-10-08', '13:00:00', '2022-10-11', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Suíte Single', 250.00, NULL),
(143, 143, 1, '003', 1, '2024-10-18', '13:00:00', '2024-10-19', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Manoel  Pedro Santos Conceição (namorado) Programador\r\nEngenharia de computação (09/02/1991)\r\nBraga - Portugal', 180.00, NULL),
(144, 144, 1, '002', 0, '2022-10-10', '13:00:00', '2022-10-10', '12:00:00', '', '', '', 'Automovel', '', 220.00, NULL),
(145, 145, 1, '002', 0, '2022-10-04', '13:00:00', '2022-10-05', '12:00:00', '', '', '', '', '', 80.00, NULL),
(146, 146, 1, '001', 1, '2024-11-23', '13:00:00', '2024-11-24', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', '(Cuidadora) - Maria de Jesus Amaral Apolinário', 200.00, NULL),
(147, 147, 1, '002', 1, '2024-11-23', '13:00:00', '2024-11-24', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa) - Eugênia Maria Araújo', 180.00, NULL),
(256, 138, 1, '003', 1, '2023-02-18', '13:00:00', '2023-02-20', '12:00:00', '', '', 'Ferias', '', 'Ana Maria Araújo dos Santos (esposa)', 480.00, NULL),
(257, 138, 1, '003', 1, '2024-03-28', '13:00:00', '2024-03-31', '12:00:00', '', '', 'Ferias', '', 'Ana Maria Araújo  dos Santos (esposa)', 480.00, NULL),
(259, 91, 1, '003', 1, '2024-12-04', '13:00:00', '2024-12-05', '12:00:00', '', '', 'Negocios', '', 'Antônio Ednardo Oliveira Andrade', 170.00, NULL),
(260, 63, 1, '001', 0, '2024-07-20', '13:00:00', '2024-07-22', '12:00:00', '', '', 'Negocios', 'Automovel', '', 120.00, NULL),
(261, 63, 1, '001', 0, '2024-08-23', '13:00:00', '2024-08-25', '12:00:00', '', '', 'Ferias', 'Automovel', '', 120.00, NULL),
(262, 63, 1, '001', 0, '2024-09-17', '13:00:00', '2024-09-19', '12:00:00', '', '', 'Negocios', 'Automovel', '', 120.00, NULL),
(263, 63, 1, '001', 0, '2024-10-18', '13:00:00', '2024-10-20', '12:00:00', '', '', 'Negocios', 'Automovel', '', 120.00, NULL),
(264, 63, 1, '001', 0, '2024-11-12', '13:00:00', '2024-11-14', '12:00:00', '', '', 'Negocios', 'Automovel', '', 120.00, NULL),
(265, 63, 1, '001', 0, '2024-12-03', '13:00:00', '2024-12-05', '12:00:00', '', '', 'Negocios', 'Automovel', '', 120.00, NULL),
(266, 258, 1, '002', 1, '2024-12-04', '13:00:00', '2024-12-05', '12:00:00', '', 'Sebrae sem fome', 'Negocios', '', 'Danielle Silva - pedagoga', 140.00, NULL),
(267, 259, 1, '002', 0, '2024-12-09', '13:00:00', '2024-12-10', '12:00:00', '', '', 'Ferias', '', '', 180.00, NULL),
(269, 65, 1, '001', 4, '2024-12-29', '13:00:00', '2025-01-02', '12:00:00', '', '', 'Ferias', 'Automovel', 'AULECI DA SILVA SIQUEIRA BARBOSA  \r\nBEATRIZ SIQUEIRA BARBOSA\r\nQUELLE DE SANTANA BARBOSA\r\n JORGE LUCAS S BARBOSA\r\n', 1920.00, 'CPF e profissão dos acompanhantes em ordem:\r\nCPF 632799593 49 Professora\r\nCPF  055755283 40 Arquiteta\r\nCPF 047.578.005-17  MISSIONÁRIA\r\nCPF 055.024.743-21 Professor\r\n\r\n'),
(272, 107, 1, '003', 1, '2024-12-14', '13:00:00', '2024-12-15', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Outro', '', '(Esposa) Ana Maria', 180.00, 'Viagem passeio'),
(273, 1, 1, '001', 1, '2024-12-14', '13:00:00', '2024-12-15', '12:00:00', 'Fortaleza', 'vicosa do ceara', '', '', '(Filho) Gabriel Angelus Cândido Oliveira', 160.00, 'Rua 06 123 Jandaiguaba - Caucáia-Ce'),
(274, 265, 1, '002', 1, '2024-12-14', '13:00:00', '2024-12-15', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Negocios', 'Automovel', '(Filho) Gabriel', 160.00, 'Show em casamento na pousada Vivenda Juarez'),
(275, 68, 1, '001', 1, '2024-12-26', '13:00:00', '2024-12-28', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Diana Galeno da Silva Castro', 380.00, 'Passeio antes Reveilhon'),
(276, 267, 1, '007', 0, '2024-12-27', '13:00:00', '2024-12-29', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Montadores moveis do Banco do Brasil\r\nGeraldo Ferreira da Silva', 200.00, 'Duas diárias por 200,00 cada um'),
(277, 268, 1, '007', 1, '2024-12-27', '13:00:00', '2024-12-29', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Negocios', 'Automovel', 'Welton Sales Meneses-Montador de móveis-BB', 200.00, ''),
(278, 266, 1, '005', 1, '2025-01-01', '13:00:00', '2025-01-02', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', 'Kamyla Azevedo Sampaio\r\n\r\nPagou 50% da Hospedagem  $ 90,00', 180.00, 'Inicio de ano novo'),
(279, 269, 1, '001', 3, '2024-12-28', '13:00:00', '2024-12-29', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Joyce Kathariny Freitas de Sousa\r\nBenício Freitas de Sousa\r\nMurilo Freitas de Sousa', 200.00, ''),
(280, 270, 1, '006', 1, '2024-12-31', '13:00:00', '2025-01-01', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Delano da Rocha Farias', 230.00, 'Reveilhon'),
(281, 271, 1, '007', 2, '2024-12-30', '13:00:00', '2025-01-01', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Outro', 'Automovel', 'Mae- Luiza Helena Monteiro da Costa\r\nFilho - Heitor Guievara Monteiro Avelino Siqueira', 700.00, 'Reveilhon 2024'),
(282, 272, 1, '002', 1, '2025-01-02', '13:00:00', '2025-01-04', '12:00:00', 'Teresina-Pi', 'Vicosa do Ceará - CE', 'Outro', 'Automovel', 'gilmar da silva teixeira', 0.00, 'ferias (Detran-PI)'),
(283, 273, 1, '002', 2, '2025-01-05', '13:00:00', '2025-01-06', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Outro', 'Automovel', 'esposa - Juciara Fernanda\r\nfilha - Ayla Lourdes', 200.00, 'passeio'),
(284, 274, 1, '004', 3, '2025-01-05', '13:00:00', '2025-01-06', '12:00:00', '', '', '', '', 'Fagoneide Silva de Oliveira\r\nAlice de Oliveira de Mesquita\r\nVittor Emanuel de Oliveira de Mesquita', 200.00, ''),
(285, 275, 1, '001', 2, '2025-01-05', '13:00:00', '2025-01-06', '12:00:00', 'Brasilia -DF', 'vicosa do ceara', 'Ferias', 'Automovel', 'Andrea Santos\r\nCaio Santos', 200.00, 'Passeio por Viçosa do Ceará'),
(286, 276, 1, '007', 1, '2025-01-05', '13:00:00', '2025-01-06', '12:00:00', 'Parnaíba-Pi', 'Barra do Corda', 'Ferias', 'Automovel', 'Felipe Santana de Sousa', 180.00, ''),
(287, 277, 1, '006', 2, '2025-01-06', '13:00:00', '2025-01-09', '12:00:00', 'Bacabal - Ma', 'vicosa do ceara', 'Ferias', 'Automovel', 'Esposa-Weline Fernandes da Silva\r\nBebe - Esdras Fernandes dos Santos (7 meses)', 480.00, 'Amidos do Pastor Ze Carlos-Montese'),
(288, 278, 1, '002', 2, '2025-01-09', '13:00:00', '2025-01-11', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Núbia Dias Costa Caetano-(Esposa)\r\nIlda Victória Dias Caetano -(Filha)', 470.00, 'Pagou 50% da hospedagem'),
(289, 279, 1, '005', 1, '2025-01-08', '13:00:00', '2025-01-10', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', 'Suzane Braga (Esposa)', 380.00, 'Cliente pagou 50% da reserva'),
(290, 280, 1, '007', 2, '1025-01-10', '13:00:00', '2025-01-11', '12:00:00', 'Teresina-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Ercicleide Melo da Silva Oliveira - Esposa\r\nSofia Torquato Melo Oliveira       -  Filha', 200.00, 'Amigos Auxiliadora da Igreja Batista de Teresina-Pi'),
(291, 147, 1, '002', 1, '2025-01-11', '13:00:00', '2025-01-12', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', 'Esposa Eugenia Maria Araújo', 180.00, ''),
(292, 281, 1, '005', 1, '2025-01-11', '13:00:00', '2025-01-12', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa) Elismar dos Anjos de Araújo', 180.00, 'Irmão da Aninha do seu Assis'),
(293, 282, 1, '004', 3, '2025-01-11', '13:00:00', '2025-01-12', '12:00:00', 'Parnaíba-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa) Mayara Bastos Ramos Araújo\r\n(Filho)      Artur Ramo0s Araújo\r\n(Filho)      Caio Ramos Araújo', 180.00, 'Sobrinho da Aninha de seu Assis'),
(294, 283, 1, '002', 1, '2025-01-16', '13:00:00', '2025-01-17', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa) Izamar de Almeida Azevedo', 140.00, 'Irmãos da Cidade de Moitas de Amontada-Ce'),
(295, 285, 1, '004', 1, '2025-01-16', '13:00:00', '2025-01-17', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa) Lasthênia Maria de Albuquerque Paiva', 180.00, 'Clientes com comorbidades'),
(296, 91, 1, '007', 1, '2025-01-20', '13:00:00', '2025-01-21', '12:00:00', 'Piripiri', 'vicosa do ceara', 'Negocios', 'Automovel', 'Antonio Evanardo de Oliveira Andrade', 190.00, 'Fibra oticas'),
(297, 286, 1, '001', 3, '2025-01-25', '13:00:00', '2025-01-26', '12:00:00', 'Piripiri-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa)  Fernanda de Carvalho Santos\r\n(Filha) Maria Clara Santos de Melo\r\n(Filho) Paulo Arthur Santos de Melo', 240.00, ''),
(298, 287, 1, '002', 2, '2025-01-25', '13:00:00', '2025-01-26', '12:00:00', 'Piripiri-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa) Mirna Rafaela dos Santos Sousa\r\nFilho  Bernardo Cunha Alves', 220.00, ''),
(299, 288, 1, '003', 2, '2025-01-25', '13:00:00', '2025-01-26', '12:00:00', 'Piripiri-Pi', 'vicosa do ceara', 'Ferias', 'Automovel', '(Esposa) Isaína Maria de Andrade\r\nFilho - Victor Gabriel  Andrade Ferreira', 220.00, ''),
(301, 291, 1, '002', 0, '2025-03-03', '13:00:00', '2025-03-05', '12:00:00', 'Fortaleza', 'vicosa do ceara', 'Outro', 'Automovel', '', 500.00, 'Período Carnaval'),
(302, 292, 1, '003', 2, '2025-06-19', '13:00:00', '2025-06-22', '12:00:00', 'Euzébio - Ce', 'Viçosa do Ceará-CE', 'Outro', 'Automovel', 'Lindalva Lima Moreira - (Esposa)\r\nLohana Lima               -  (Neta)\r\nPAGOU ENTRADA DE 50% NO VALOR DE $ 938,00', 185.00, 'FESTIVAL VIÇOSA  MEL & CACHAÇA\r\nJUNHO DE 2025'),
(303, 293, 1, '002', 1, '2025-06-19', '13:00:00', '2025-06-22', '12:00:00', 'Fortaleza-CE', 'Viçosa do Ceará-CE', 'Outro', 'Automovel', 'Francina Nunes de Oliveira (Esposa)\r\nValor total da reserva $ 1.350,00 Referente à 03 diárias\r\nCliente pagou 50% na reserva no valor de $ 675,00', 1350.00, 'FESTIVAL VIÇOSA MEL & CACHAÇA DE 2025'),
(304, 43, 1, '005', 1, '2025-06-19', '13:00:00', '2025-06-22', '12:00:00', 'Fortaleza', 'FESTIVAL MEL E CACHAÇA 2025', 'Outro', 'Automovel', '(Esposa) NILCE CLICIA MAIA QUEIROZ\r\nVALOR TOTAL DE 3 DIARIAS $ 1.350,00 (QUITADO)', 1.35, 'FESTIVAL MEL E CACHAÇA 2025'),
(305, 294, 1, '001', 3, '2025-06-19', '13:00:00', '2025-06-22', '12:00:00', 'Fortaleza', 'VIÇOSA DO CEARÁ-CE', '', 'Automovel', 'TEREZINHA LISIEUX DE CASTRO LEITÃO\r\nLISETANIA MARIA DE CASTRO LEITÃO\r\nJOÃO VICTOR BARBOZA LEITÃO\r\nRESERVA QUITADA NO VALOR 2.400,00', 2400.00, 'FESTIVAL MEL E CACHAÇA 2025'),
(306, 295, 1, '004', 1, '2025-06-19', '13:00:00', '2025-06-22', '12:00:00', 'Fortaleza', 'VIÇOSA DO CEARÁ-CE', '', 'Automovel', 'MIRIAN CAMPOS DIONÍSIO MAIA - (ESPOSA)\r\nPAGOU 50% NA RESERVA NO VALOR DE $ 675,00', 1350.00, 'FESTIVAL MEL E CACHAÇA 2025'),
(307, 296, 1, '006', 1, '0025-06-19', '13:00:00', '0025-06-22', '12:00:00', 'MARACANAÚ- FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', '', 'Automovel', '(ESPOSA) MARIA ERIOSVELMA FREIRE OLIVEIRA\r\nCLIENTE PAGOU RESERVA DE $ 675,00', 1350.00, 'FESTIVAL MEL E CACHAÇA 2025'),
(308, 42, 1, '007', 1, '2025-06-19', '13:00:00', '2025-06-22', '12:00:00', 'PIRIPIRI-PIAUÍ', 'VIÇOSA DO CEARÁ-CE', '', 'Automovel', 'JOSÉLIA DA COSTA (ESPOSA)', 1350.00, 'FESTIVAL MEL E CACHAÇA 2025'),
(309, 297, 1, '001', 3, '2025-03-03', '13:00:00', '2025-03-05', '12:00:00', 'EUZÉBIO - FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', '', 'Automovel', 'ESPOSO - FRANCISCO EVANDRO DE OLIVEIRA CARVALHO\r\nFILHA      - REBECA MENDES CARVALHO\r\nFILHA      - SARA MONALISA ANDRADE', 780.00, 'CARNAVAL 2025'),
(310, 298, 1, '007', 1, '2025-02-08', '13:00:00', '2025-02-09', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'MARIA ZINETH RAMOS LIMA', 180.00, ''),
(311, 299, 1, '006', 1, '2025-03-01', '13:00:00', '2025-03-05', '12:00:00', 'TERESINA-PI', 'VIÇOSA DO CEARÁ-CE', '', 'Automovel', 'SUELLDO SOUSA MORAIS  -  (ESPOSO)', 0.00, 'CARNAVAL 2025'),
(312, 300, 1, '005', 1, '2025-03-01', '13:00:00', '2025-03-05', '12:00:00', 'TERESINA-PI', 'VIÇOSA DO CEARÁ-CE', '', 'Automovel', 'SOLANGE MARIA FERNANDES DA SILVA', 640.00, 'CARNAVAL 2025'),
(313, 302, 1, '004', 1, '2025-03-01', '13:00:00', '2025-03-03', '12:00:00', 'ACARAÚ-CE', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', '(ESPOSA) - JOELMA ROCHA MORAES', 500.00, 'CARNAVAL 2025'),
(314, 303, 1, '004', 1, '0025-03-03', '13:00:00', '0025-03-05', '12:00:00', 'Teresina-Pi', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'FRANCISCA ITAYLANNE DE CARVALHO RÊGO', 440.00, 'CARNAVAL 2025'),
(315, 298, 1, '002', 1, '2025-03-15', '13:00:00', '2025-02-16', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'Maria Zineth Lima', 180.00, 'Final de semana'),
(316, 304, 1, '001', 1, '2025-03-15', '13:00:00', '2025-03-16', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'AUGUSTO BRAUNA LOPES (ESPOSO)', 180.00, ''),
(317, 305, 1, '008', 1, '2025-06-19', '13:00:00', '2025-06-22', '12:00:00', 'Fortaleza', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'OSSIAN PAIVA DE OLIVEIRA - (ESPOSO)', 1500.00, 'FESTIVAL MEL E CACHAÇA'),
(318, 306, 1, '007', 2, '2025-03-21', '13:00:00', '2025-03-27', '12:00:00', 'SANTA MARIA DA VITÓRIA-BAHIA', 'Vicosa do Ceará - CE', 'Negocios', 'Automovel', 'CINARA CASTILLO (ESPOSA)\r\nMIRELLA VITÓRIA CASTILLO NICOLICHE', 1500.00, 'NEGÓCIOS'),
(319, 307, 1, '002', 1, '2025-04-18', '13:00:00', '2025-04-21', '12:00:00', 'SÂO LUÌS-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'MARINA SANTOS SILVA DE OLIVEIRA (ESPOSA)', 570.00, 'SEMANA SANTA 2025'),
(320, 308, 1, '003', 1, '2025-04-18', '13:00:00', '2025-04-21', '12:00:00', 'SÂO LUÍS-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'ISNADIA CRISTINA RIBEIRO COSTA - (ESPOSA|)', 570.00, 'SEMANA SANTA 2025'),
(321, 309, 1, '001', 1, '2025-04-18', '13:00:00', '2025-04-20', '12:00:00', 'TAUA', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'JOSÉ AIRTON', 480.00, 'SEMANA SANTA 2025'),
(322, 310, 1, '009', 1, '2025-06-19', '13:00:00', '2025-06-22', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'GEORGIA FABIANA MENDES MARINHO', 900.00, 'FESTIVAL MEL E CACHAÇA 2025'),
(323, 311, 1, '001', 2, '2025-04-18', '13:00:00', '2025-04-20', '12:00:00', 'São Luis-MA', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'YURI CASTELO BRANCO SANTOS - (ESPOSO)\r\nJOÃO LUCAS CASTELO BRANCO - (FILHO)', 460.00, 'SEMANA SANTA 2025'),
(324, 312, 1, '003', 1, '2025-04-20', '13:00:00', '2025-04-21', '12:00:00', 'Teresina-Pi', 'VIÇOSA DO CEARÁ-CE', 'Saude', 'Automovel', 'LUCYENE MARIA NERY ALVES\r\nMARIA DULCE SILVA\r\nJOANA LÚCIA FEITOSA NETA-226.498.323-04', 380.00, 'SEMANA SANTA-FERIADO 21 DE ABRIL'),
(582, 510, 1, '001', 3, '2025-05-02', '13:00:00', '2025-05-04', '12:00:00', 'PIRIPIRI-PIAUÍ', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'THIAGO FERRO DE MORAES-ESPOSO\r\nAURORA ARCANGELO VERAS- FILHAS\r\nARTEMIS ARCANGELO VERAS-FILHAS GEMEAS\r\nATENA ARCANGELO CERAS- 07 ANOS', 440.00, 'ABERTURA DO FESTIVAL MEL E CACHAÇA/2025'),
(583, 511, 1, '006', 1, '2025-05-03', '13:00:00', '2025-05-04', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'MARCELO CARDOSO DOS SANTOS', 260.00, 'LANÇAMENTO MEL CAÇHACA'),
(584, 512, 1, '001', 3, '2025-05-03', '13:00:00', '2025-05-04', '12:00:00', 'CHAVAL-CE', 'VIÇOSA DO CEARÁ-CE', 'Saude', 'Automovel', 'CARLOS VINÍCIUS ROCHA VERAS-PAI\r\nJARMELINE ROCHA DE ARAÚJO-MAE\r\nVITÓRIA MARIA ARAÚJO VERAS-IRMÃ', 480.00, 'LACÇAMENTO MEL E CACHAÇA'),
(585, 513, 1, '003 ', 1, '0000-00-00', '13:00:00', '0000-00-00', '12:00:00', 'Piripiri', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'THALYSON TOMÁS ARCÂNGELO BRITO - FILHO', 440.00, 'LANÇAMENTO MEL E CACHAÇA'),
(586, 514, 1, '004', 2, '2025-05-02', '13:00:00', '2025-05-04', '12:00:00', 'PIRIPIRI-PIAUÍ', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Outro', 'SANDRA VASCONCELOS FERRO - FILHA\r\nEDVALDO XIMENES DE MORAES JÚNIOR - FILHO', 600.00, 'LANÇAMENTO MEL E CACHAÇA '),
(594, 518, 1, '003', 3, '2025-05-24', '13:00:00', '2025-05-25', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'JUÇARA ARRAIS-ESPOSA\r\nMIGUEL ARRAIS - FILHO\r\nROMEU ARRAIS - FILHO', 180.00, 'CONCURSO EM TIANGUA'),
(595, 68, 1, '002', 1, '2025-05-24', '13:00:00', '2025-05-25', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'DIANA GALENO DA SILVA CASTRO-ESPOSA', 180.00, ''),
(597, 519, 1, '008', 1, '2025-05-31', '13:00:00', '2025-06-01', '12:00:00', 'NOVA RUSSAS-CE', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'EDUARDO OLIVEIRA SOUSA - ESPOSO', 180.00, ''),
(598, 520, 1, '008', 1, '2025-06-06', '13:00:00', '2025-06-07', '12:00:00', 'URUOCA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'RAIMUNDO FONTENELLE MAGALHAES', 180.00, ''),
(599, 521, 1, '004', 0, '2025-06-11', '13:00:00', '2025-06-13', '12:00:00', 'ESPIRITO SANTO-VITÓRIA', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', '', 180.00, ''),
(600, 522, 1, '003', 1, '2025-06-14', '13:00:00', '2025-06-16', '12:00:00', 'Cocal da Estação-Pi', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'GABRIELLY VITÓRIA DE OLIVEIRA SILVA - GABY-SECRETÁRIA', 400.00, ''),
(601, 523, 1, '002', 1, '2025-06-13', '13:00:00', '2025-06-15', '12:00:00', 'CANINDÉ=CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'TÂNIAMARA NASCIMENTO GONÇALVES DA SILVA', 360.00, 'CURSO EM VIÇOSA'),
(602, 6, 1, '004', 2, '2025-08-22', '13:00:00', '2025-08-24', '12:00:00', 'Piripiri', 'VIÇOSA DO CEARÁ-CE', 'Congresso', 'Automovel', 'SELMA ANDRADE-ESPOSA\r\nIAGO ANDRADE-FILHO', 520.00, 'TESTEMUNHAS DE JEOVÁ'),
(603, 524, 1, '005', 1, '2025-06-14', '13:00:00', '2025-06-15', '12:00:00', 'Fortaleza', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'ANTONIO H. ROCHA', 180.00, ''),
(604, 525, 1, '007', 1, '2025-06-14', '13:00:00', '2025-06-15', '12:00:00', 'VARJOTA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'WILLIAN FARIAS TEODORO DA SILVA', 180.00, 'CURSO OCILOSCÓPIO-COM MARCIO DA MULTICAR'),
(605, 526, 1, '001', 2, '2025-06-27', '13:00:00', '2025-06-28', '12:00:00', 'Teresina-Pi', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'LUZIA SANTOS DO NASCIMENTO\r\nSAMUEL VICTOR BARBOSA DA SILVA (NETO)', 220.00, 'CASAMENTO NA MEYRE SILVA'),
(606, 527, 1, '003', 2, '2025-06-27', '13:00:00', '2025-06-28', '12:00:00', 'Teresina-Pi', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'JUSCELINO MONTEIRO LIMA\r\nFRANCISCO LENILSON ALVES NUNES', 220.00, 'EVENTO DE CASAMENTO NA MEYRE SILVA'),
(607, 527, 1, '001', 1, '2025-06-28', '13:00:00', '2025-06-30', '12:00:00', 'Teresina-Pi', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'FRANCISCO LENILSON ALVES NUNES', 150.00, 'EVENTO DE CASAMENTO NA MEYRE SILVA'),
(609, 529, 1, '004', 0, '2025-07-07', '13:00:00', '2025-07-12', '12:00:00', 'Fortaleza', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Outro', '', 400.00, 'ESPOSA DO PASTOR BARBOSA'),
(610, 530, 1, '003', 2, '2025-07-23', '13:00:00', '2025-07-27', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'JOSELANE DE FARIAS SOUZA-ESPOSA\r\nISADORA DE FARIAS SOUZA-FILHA', 800.00, 'FÉRIAS E FESTIVAL MI 2025'),
(611, 531, 1, '008', 1, '2025-07-11', '13:00:00', '2025-07-12', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'EDMAR PEREIRA DO NASCIMENTO-ESPOSO', 180.00, 'FÉRIAS DE JULHO E MI VIÇOSA/2025'),
(612, 531, 1, '003', 2, '2025-07-11', '13:00:00', '2025-07-12', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'RITA DE CÁSSIA \r\nLUCIMAR PEREIRA\r\nANA MARIA ', 270.00, 'RESERVA FEITA NO NOME DE VIRGÍNIA  MARIA BERTOLDO'),
(613, 531, 1, '005', 0, '2025-07-11', '13:00:00', '2025-07-12', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Aviao', 'RIBAMAR PEREIRA-QUARTO INDIVIDUAL', 90.00, 'RESERVA FEITA POR VIRGÍNIA MARIA BERTOLDO'),
(614, 138, 1, '003', 3, '2025-07-12', '13:00:00', '2025-07-13', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'ANALINE\r\nESPOSO\r\nAS MENINAS', 190.00, ''),
(615, 138, 1, '008', 1, '2025-07-12', '13:00:00', '2025-07-13', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'ANA MARIA - ESPOSA', 190.00, ''),
(616, 46, 1, '002', 1, '2025-07-11', '13:00:00', '2025-07-13', '12:00:00', 'PARAIPABA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'RENATA LIMA VIANA', 380.00, ''),
(617, 533, 1, '001', 1, '2025-07-11', '13:00:00', '2025-07-13', '12:00:00', 'PARAIPABA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'THALITA SILVA SOUSA-ESPOSA', 380.00, 'GRUPO DE ÁLVARO MAURÍCIO DUARTE FREITAS'),
(619, 534, 1, '006', 0, '2025-07-11', '13:00:00', '2025-07-13', '12:00:00', 'PARAIPABA-CE', 'VIÇOSA DO CEARÁ-CE', '', 'Automovel', 'QUARTO INDIVIDUAL', 220.00, 'GRUPO DE ÁLVARO MAURÍCIO DUARTE FREITAS'),
(620, 532, 1, '002', 1, '2025-07-11', '13:00:00', '2025-07-13', '12:00:00', 'PARAIPABA-CE', '', 'Ferias', 'Automovel', 'RENATA LIMA VIANA', 380.00, ''),
(621, 311, 1, '002', 1, '2025-07-16', '13:00:00', '2025-07-17', '12:00:00', 'SÃO JOSÉ DE RIBAMAR-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'YURI CASTELO BRANCO-ESPOSO', 190.00, ''),
(622, 535, 1, '008', 1, '2025-07-18', '13:00:00', '2025-07-21', '12:00:00', 'Teresina-Pi', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'ESTEFAN COELHO SILVA-ESPOSO', 570.00, ''),
(623, 536, 1, '002', 1, '2025-08-19', '13:00:00', '2025-08-24', '12:00:00', 'CAUCÁIA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Outro', 'GILVÂNIA S. DA C. GONÇALVES-IRMÃ', 1000.00, 'SEMANA DE FÉRIAS EM VIÇOSA-CE'),
(624, 283, 1, '006', 1, '2025-07-09', '13:00:00', '2025-07-11', '12:00:00', 'MOITAS-AMONTADA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'IZAMAR DE ALMEIDA AZEVEDO - ESPOSA', 350.00, ''),
(625, 284, 1, '007', 2, '2025-07-09', '13:00:00', '2025-07-11', '12:00:00', 'MOITAS-AMONTADA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'JULIANNE SOARES AZEVEDO-ESPOSA\r\nMARIA JULIA SOARES AZEVEDO-FILHA', 350.00, ''),
(626, 537, 1, '005', 1, '2025-07-12', '13:00:00', '2025-07-13', '12:00:00', 'Brasilia -DF', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'LUCIMAR MACEDO DOS SANTOS DE SOUZA', 150.00, ''),
(627, 538, 1, '007', 1, '2025-07-12', '13:00:00', '2025-07-13', '12:00:00', 'BRASILIA-DF', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'ANTONIA CAROLINA DE SOUZA', 150.00, 'CONHECIDOS DO RODRIGO P.DA ONÇA');
INSERT INTO `reservas` (`id`, `hospede_id`, `pousada_id`, `uh`, `numacomp`, `dataentrada`, `horaentrada`, `datasaida`, `horasaida`, `vemde`, `vaipara`, `motivo`, `transporte`, `acompanhantes`, `valor`, `observacao`) VALUES
(628, 88, 1, '004', 1, '2025-07-15', '13:00:00', '2025-07-16', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'KAIKE VICTOR GONÇALVES FREITAS', 150.00, ''),
(629, 539, 1, '003', 2, '2025-07-18', '13:00:00', '2025-07-19', '12:00:00', 'MOITAS DE AMONTADA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'LEILA DAIANE ROSA DOS SANTOS-ESPOSA - 10/10/1987\r\nSAMUEL PEREIRA DE AZEVEDO NETO-FILHO - 15/05/2014', 240.00, 'AMIGO DO IRMÃO JOEL E IZAMAR'),
(630, 540, 1, '004', 1, '2025-07-18', '13:00:00', '2025-07-19', '12:00:00', 'MOITAS-AMONTADA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'GISELE MARQUES DE FREITAS-FILHA', 190.00, 'FAMILIA DO IRMÃO JAIRO PEREIRA'),
(631, 541, 1, '002', 1, '2025-07-24', '13:00:00', '2025-07-26', '12:00:00', 'SOBRAL-CE', 'VIÇOSA DO CEARÁ-CE', 'Congresso', 'Automovel', 'FRANCISCA LILIAN DA SILVA', 380.00, ''),
(632, 542, 1, '003', 2, '2025-04-18', '13:00:00', '2025-04-20', '12:00:00', 'SAO LUIS-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'YURI CASTELO BRANCO SANTOS\r\nJOÃO LUCAS CASTELO BRANCO DUTRA', 460.00, ''),
(633, 543, 1, '006', 1, '2025-07-21', '13:00:00', '2025-07-22', '12:00:00', 'TIMON-MA', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'JOÃO PAULO LIMA RIBEIRO BORGES', 180.00, ''),
(634, 544, 1, '002', 1, '2025-07-26', '13:00:00', '2025-07-27', '12:00:00', 'ARAGUAINA TOCANTINS', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Aviao', 'MARIA LUCILANDIA DIAS LEITE-ESPOSA', 200.00, ''),
(635, 545, 1, '004', 3, '2025-07-26', '13:00:00', '2025-07-27', '12:00:00', 'ARAGUAÍNA -TOCANTINS', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'KIMILLY LEITE RODRIGUES - FILHOS JARDEL\r\nKERLLY LEITE RODRIGUES - FILHOS JARDEL\r\nLUCAS CRISTH REGO LEITE - FILHOS JARDEL', 300.00, ''),
(636, 546, 1, '002', 1, '2025-07-19', '13:00:00', '2025-07-20', '12:00:00', 'PALMAS-TOCANTINS', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'RAPHAEL CACINELLI', 200.00, ''),
(637, 544, 1, '003', 2, '2025-07-19', '13:00:00', '2025-07-20', '12:00:00', 'MOITAS AMONTADA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'LEILA DAIANE ROSA DOS SANTOS\r\nSAMUEL PEREIRA DE AZEVEDO NETO', 190.00, ''),
(638, 540, 1, '004', 1, '2025-07-19', '13:00:00', '2025-07-20', '12:00:00', 'MOITAS-AMONTADA-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'GISELE MARQUES DE FREITAS-FILHA', 190.00, ''),
(639, 547, 1, '002', 3, '2025-07-21', '13:00:00', '2025-07-22', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'MAICOM\r\nWAGNER\r\nFRANCISCO DANILO', 350.00, ''),
(640, 547, 1, '004', 3, '2025-07-21', '13:00:00', '2025-07-22', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'ADROALDO\r\nVLADIMIR\r\nGABRIEL', 350.00, ''),
(641, 547, 1, '007', 2, '2025-07-21', '13:00:00', '2025-07-22', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'RENATO\r\nCARLOS ALBERTO', 350.00, ''),
(642, 548, 1, '008', 1, '2025-07-26', '13:00:00', '2025-07-27', '12:00:00', 'TERESINA-PI', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'ANNA CAROLLYNA PINHEIRO FERNANDES', 240.00, 'FESTIVAL MI/2025'),
(644, 550, 1, '005', 1, '2025-07-26', '13:00:00', '2025-07-27', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'VILMA', 260.00, 'ANIVERSÁRIO DE POLIANA'),
(646, 551, 1, '006', 1, '2025-07-26', '13:00:00', '2025-07-27', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'ÉRICA', 260.00, 'ANIVERSÁRIO DE POLIANA'),
(647, 552, 1, '007', 1, '2025-07-26', '13:00:00', '2025-07-27', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'KEILA', 260.00, 'ANIVERSÁRIO DE POLIANA'),
(648, 553, 1, '001', 3, '2025-07-26', '13:00:00', '2025-07-27', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'CILMA-ESPOSA\r\nMATHEUS-FILHO\r\nMARIANA-FILHA', 520.00, 'ANIVERSARIO DE POLLIANA'),
(649, 554, 1, '005', 1, '2025-07-21', '13:00:00', '2025-07-22', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'DANIEL BRASIL DE ABREU\r\nDOUGLAS HENRIQUE', 190.00, ''),
(650, 555, 1, '008', 1, '2025-07-21', '13:00:00', '2025-07-22', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Congresso', 'Automovel', 'MAYRA QUEYROZ', 240.00, ''),
(651, 556, 1, '006', 1, '2025-07-22', '13:00:00', '2025-07-23', '12:00:00', 'Teresina-Pi', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'DAVID RONALDO MAGALHAÊS DA SILVA', 200.00, ''),
(652, 554, 1, '005', 1, '2025-07-22', '13:00:00', '2025-07-23', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'DANIEL BRASIL DE ABREU\r\nDOUGLAS HENRIQUE', 190.00, ''),
(653, 557, 1, '001', 3, '2025-07-24', '13:00:00', '2025-07-26', '12:00:00', 'AQUIRAZ-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'RENATA CORREIA DE FREITAS-ESPOSA\r\nRUTE DE FREITAS ALCANTARA- FILHA\r\nJOÃO DE FREITAS ALCANTARA-FILHO', 540.00, ''),
(654, 558, 1, '004', 4, '2025-07-23', '13:00:00', '2025-07-25', '12:00:00', 'CHAPADINHA-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'MARIA DO ROSARIO COSTA DA SILVA-ESPOSA\r\nDANIEL DA SILVA-FILHO\r\nJOÃO LUCAS COSTA DA SILVA-FILHO\r\nALICE COSTA DA SILVA-BEBE\r\nAMANDA SILVA-SOBRINHA', 600.00, ''),
(655, 559, 1, '006', 3, '2025-07-23', '13:00:00', '2025-07-25', '12:00:00', 'CHAPADINHA-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'LAYANE BARBOSA DE SOUSA\r\nANA LIVIA AGUIAR SOUSA\r\nANA LUISA AGUIAR SOUSA', 560.00, ''),
(656, 560, 1, '008', 1, '2025-07-23', '13:00:00', '2025-07-25', '12:00:00', 'CHAPADINHA-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'NATALIA ARAUJO DA COSTA', 440.00, ''),
(657, 558, 1, '004', 4, '2025-07-23', '13:00:00', '2025-07-25', '12:00:00', 'CHAPADINHA-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'MARIA DO ROSARIO COSTA DA SILVA-ESPOSA\r\nJOAO LUCAS COSTA SILVA\r\nALICE COSTA DA SILVA-BEBE\r\nAMANDA-SOBRINHA', 600.00, ''),
(658, 561, 1, '005', 1, '2025-07-24', '13:00:00', '2025-07-25', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'KEDMA ARRUDA MESQUITA', 220.00, ''),
(659, 562, 1, '007', 0, '2025-07-24', '13:00:00', '2025-07-25', '12:00:00', 'Teresina-Pi', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', '', 150.00, 'AMIGO DO SUELDO MORAIS.'),
(660, 563, 1, '007', 0, '2025-07-28', '13:00:00', '2025-08-01', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'RESERVA FEITA POR FLYTOUR FORTALEZA', 560.00, ''),
(661, 129, 1, '008', 0, '2025-07-29', '13:00:00', '2025-07-31', '12:00:00', 'COCAL DA ESTAÇÃO -PI', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', '', 200.00, 'VENDEDOR DE SEMI-JÓIAS'),
(662, 564, 1, '006', 1, '2025-09-06', '13:00:00', '2025-09-07', '12:00:00', 'SÃO LUÍS-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'LAÍSE FRANCY PEREIRA MATOS', 200.00, ''),
(663, 563, 1, '007', 0, '2025-08-11', '13:00:00', '2025-08-15', '12:00:00', 'FORTALEZA-CE', 'VIÇOSA DO CEARÁ-CE', 'Negocios', 'Automovel', 'RESERVA  FEITA POR IDRB VIAGENS TURISMO LTDA\r\nCNPJ 05.324.630.0001-35\r\nCONSULTORA FLYTOUR FORTALEZA (85 9983-1489)', 560.00, ''),
(664, 68, 1, '003', 2, '2025-07-29', '13:00:00', '2025-07-30', '12:00:00', 'PARNAÍBA-PI ', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Aviao', 'DIANA GALENO-ESPOSA\r\nBEATRIZ GALENO-FILHA', 230.00, 'ANIVERSÁRIO DA BEATRIZ E SHOW DA ANLINE BARROS EM TIANGUÁ'),
(665, 565, 1, '005', 1, '2025-09-06', '13:00:00', '2025-09-07', '12:00:00', 'SOBRAL-CE', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'EDINALDO GONÇALVES NUNES JÚNIOR', 200.00, ''),
(666, 146, 1, '004', 1, '2025-08-09', '13:00:00', '2025-08-10', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'MARIA DE JESUS AMARAL APOLINÁRIO-CUIDADORA', 190.00, ''),
(667, 138, 1, '008', 1, '2025-08-09', '13:00:00', '2025-08-10', '12:00:00', 'Parnaíba-Pi', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'ANA MARIA -ESPOSA', 190.00, 'COMEMORAR DIA DOS PAIS'),
(668, 566, 1, '005', 1, '2025-08-09', '13:00:00', '2025-08-10', '12:00:00', 'PARNAÍBA-PI ', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'ELISMAR-ESPOSA', 190.00, 'COMEMORAR DIA DOS PAIS'),
(669, 567, 1, '006', 2, '2025-08-09', '13:00:00', '2025-08-10', '12:00:00', 'PARNAÍBA-PI ', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Automovel', 'EUGÊNIA-ESPOSA\r\nJOSÉ VITOR-FILHO', 190.00, 'COMEMORAR DIA DOS PAIS'),
(670, 568, 1, '003', 3, '2025-08-09', '13:00:00', '2025-08-10', '12:00:00', 'PARNAÍBA-PI ', 'VIÇOSA DO CEARÁ-CE', 'Outro', 'Aviao', 'ANALINE-ESPOSA\r\nMANUELLA-FILHA\r\nMARIANA-FILHA', 190.00, 'COMEMORAR DIA DOS PAIS'),
(671, 569, 1, '007', 1, '2025-09-06', '13:00:00', '2025-09-07', '12:00:00', 'SÂO LUÌS-MA', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'ELIANE OLIVEIRA SANTOS-ESPOSA', 200.00, 'CASAL AMIGO DE CARLOS ANDRÉ'),
(672, 18, 1, '008', 1, '2025-08-15', '13:00:00', '2025-08-17', '12:00:00', '', 'VIÇOSA DO CEARÁ-CE', 'Ferias', 'Automovel', 'JOSÉ JUACIR', 380.00, ''),
(673, 1, 1, '005', 1, '2025-08-10', '13:00:00', '2025-08-11', '12:00:00', '', '', '', '', 'marido', 156.00, '');

-- --------------------------------------------------------

--
-- Estrutura para tabela `sql_query_history`
--

CREATE TABLE `sql_query_history` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `pousada_id` int NOT NULL DEFAULT '0',
  `query_text` text NOT NULL,
  `executed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` decimal(10,4) DEFAULT NULL,
  `affected_rows` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `sql_query_history`
--

INSERT INTO `sql_query_history` (`id`, `user_id`, `pousada_id`, `query_text`, `executed_at`, `execution_time`, `affected_rows`) VALUES
(1, 32, 0, 'SELECT \r\n    \'CPF Duplicado\' as tipo_duplicata,\r\n    cpf,\r\n    COUNT(*) as quantidade,\r\n    GROUP_CONCAT(CONCAT(id, \' - \', nome, \' (\', cidade, \')\') SEPARATOR \' | \') as hospedes_encontrados\r\nFROM hospedes \r\nWHERE cpf IS NOT NULL \r\n  AND cpf != \'\' \r\n  AND LENGTH(cpf) = 11\r\nGROUP BY cpf, pousada_id\r\nHAVING COUNT(*) > 1\r\n\r\nUNION ALL\r\n\r\n-- Critério 2: Mesmo nome e data de nascimento\r\nSELECT \r\n    \'Nome + Data Nascimento\' as tipo_duplicata,\r\n    CONCAT(nome, \' - \', nasc) as identificador,\r\n    COUNT(*) as quantidade,\r\n    GROUP_CONCAT(CONCAT(id, \' - \', nome, \' (\', COALESCE(cpf, \'Sem CPF\'), \')\') SEPARATOR \' | \') as hospedes_encontrados\r\nFROM hospedes \r\nWHERE nome IS NOT NULL \r\n  AND nasc IS NOT NULL\r\nGROUP BY UPPER(TRIM(nome)), nasc, pousada_id\r\nHAVING COUNT(*) > 1\r\n\r\nUNION ALL\r\n\r\n-- Critério 3: Mesmo email (se preenchido)\r\nSELECT \r\n    \'Email Duplicado\' as tipo_duplicata,\r\n    email,\r\n    COUNT(*) as quantidade,\r\n    GROUP_CONCAT(CONCAT(id, \' - \', nome, \' (\', COALESCE(cpf, \'Sem CPF\'), \')\') SEPARATOR \' | \') as hospedes_encontrados\r\nFROM hospedes \r\nWHERE email IS NOT NULL \r\n  AND email != \'\' \r\n  AND email LIKE \'%@%\'\r\nGROUP BY LOWER(TRIM(email)), pousada_id\r\nHAVING COUNT(*) > 1\r\n\r\nUNION ALL\r\n\r\n-- Critério 4: Nome muito similar + telefone igual\r\nSELECT \r\n    \'Nome Similar + Telefone\' as tipo_duplicata,\r\n    CONCAT(\'Tel: \', telefone) as identificador,\r\n    COUNT(*) as quantidade,\r\n    GROUP_CONCAT(CONCAT(id, \' - \', nome, \' (\', COALESCE(cpf, \'Sem CPF\'), \')\') SEPARATOR \' | \') as hospedes_encontrados\r\nFROM hospedes \r\nWHERE telefone IS NOT NULL \r\n  AND telefone != \'\'\r\n  AND LENGTH(telefone) >= 10\r\nGROUP BY SOUNDEX(nome), telefone, pousada_id\r\nHAVING COUNT(*) > 1\r\n\r\nORDER BY tipo_duplicata, quantidade DESC;', '2025-08-05 09:09:09', 0.0021, 5),
(2, 32, 0, '-- Query para identificar possíveis reservas duplicadas\r\n-- Critério 1: Mesmo hóspede, mesma data de entrada e saída\r\nSELECT \r\n    \'Mesmo Hóspede - Datas Idênticas\' as tipo_duplicata,\r\n    h.nome as hospede,\r\n    r.dataentrada,\r\n    r.datasaida,\r\n    r.uh as unidade_habitacional,\r\n    COUNT(*) as quantidade_reservas,\r\n    GROUP_CONCAT(\r\n        CONCAT(\r\n            \'ID: \', r.id, \r\n            \' | UH: \', COALESCE(r.uh, \'N/A\'), \r\n            \' | Valor: R$ \', COALESCE(r.valor, 0),\r\n            \' | Entrada: \', COALESCE(r.horaentrada, \'N/A\')\r\n        ) \r\n        SEPARATOR \' || \'\r\n    ) as detalhes_reservas\r\nFROM reservas r\r\nINNER JOIN hospedes h ON r.hospede_id = h.id\r\nWHERE r.dataentrada IS NOT NULL \r\n  AND r.datasaida IS NOT NULL\r\nGROUP BY r.hospede_id, r.dataentrada, r.datasaida, r.pousada_id\r\nHAVING COUNT(*) > 1\r\n\r\nUNION ALL\r\n\r\n-- Critério 2: Mesma UH, mesmo período (conflito de ocupação)\r\nSELECT \r\n    \'Conflito de UH - Mesmo Período\' as tipo_duplicata,\r\n    CONCAT(\'UH: \', r.uh) as hospede,\r\n    r.dataentrada,\r\n    r.datasaida,\r\n    r.uh as unidade_habitacional,\r\n    COUNT(*) as quantidade_reservas,\r\n    GROUP_CONCAT(\r\n        CONCAT(\r\n            \'ID: \', r.id, \r\n            \' | Hóspede: \', h.nome,\r\n            \' | Valor: R$ \', COALESCE(r.valor, 0),\r\n            \' | Entrada: \', COALESCE(r.horaentrada, \'N/A\')\r\n        ) \r\n        SEPARATOR \' || \'\r\n    ) as detalhes_reservas\r\nFROM reservas r\r\nINNER JOIN hospedes h ON r.hospede_id = h.id\r\nWHERE r.uh IS NOT NULL \r\n  AND r.uh != \'\'\r\n  AND r.dataentrada IS NOT NULL \r\n  AND r.datasaida IS NOT NULL\r\nGROUP BY r.uh, r.dataentrada, r.datasaida, r.pousada_id\r\nHAVING COUNT(*) > 1\r\n\r\nUNION ALL\r\n\r\n-- Critério 3: Mesmo hóspede, mesmo dia de entrada, mesmo horário\r\nSELECT \r\n    \'Mesmo Hóspede - Entrada Simultânea\' as tipo_duplicata,\r\n    h.nome as hospede,\r\n    r.dataentrada,\r\n    NULL as datasaida,\r\n    GROUP_CONCAT(DISTINCT r.uh) as unidade_habitacional,\r\n    COUNT(*) as quantidade_reservas,\r\n    GROUP_CONCAT(\r\n        CONCAT(\r\n            \'ID: \', r.id, \r\n            \' | UH: \', COALESCE(r.uh, \'N/A\'), \r\n            \' | Saída: \', COALESCE(r.datasaida, \'N/A\'),\r\n            \' | Valor: R$ \', COALESCE(r.valor, 0)\r\n        ) \r\n        SEPARATOR \' || \'\r\n    ) as detalhes_reservas\r\nFROM reservas r\r\nINNER JOIN hospedes h ON r.hospede_id = h.id\r\nWHERE r.dataentrada IS NOT NULL \r\n  AND r.horaentrada IS NOT NULL\r\nGROUP BY r.hospede_id, r.dataentrada, r.horaentrada, r.pousada_id\r\nHAVING COUNT(*) > 1\r\n\r\nORDER BY tipo_duplicata, dataentrada DESC;', '2025-08-05 09:19:18', 0.0036, 20);

-- --------------------------------------------------------

--
-- Estrutura para tabela `usuarios`
--

CREATE TABLE `usuarios` (
  `id` int NOT NULL,
  `nome` varchar(50) NOT NULL,
  `senha` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `pousada_id` int NOT NULL,
  `is_admin` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Despejando dados para a tabela `usuarios`
--

INSERT INTO `usuarios` (`id`, `nome`, `senha`, `email`, `pousada_id`, `is_admin`) VALUES
(3, 'Claudio', '$2y$10$R8I90Az6oyeTgjcC1uAn4ukCYNF8sf24HfDwfFEvtqI8fnH0Tefhy', '<EMAIL>', 2, 1),
(31, 'bomviverpousada@gmail', '$2y$10$Rg82DkI.fkFbKh2g2cMFgea0Kx1gguJFSxGRatHElyUlQYZk91vBq', '<EMAIL>', 1, 1),
(32, 'a', '$2y$10$N6wu.QTtRcLeMhfunh9NXuTVbyWaC3uD4J3K.oqc1rzwrDdevbxEa', '<EMAIL>', 0, 0);

--
-- Índices para tabelas despejadas
--

--
-- Índices de tabela `caixa_diario`
--
ALTER TABLE `caixa_diario`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`),
  ADD KEY `usuario_abertura_id` (`usuario_abertura_id`),
  ADD KEY `usuario_fechamento_id` (`usuario_fechamento_id`);

--
-- Índices de tabela `categorias_financeiras`
--
ALTER TABLE `categorias_financeiras`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`);

--
-- Índices de tabela `contratos`
--
ALTER TABLE `contratos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`),
  ADD KEY `plano_id` (`plano_id`);

--
-- Índices de tabela `formas_pagamento`
--
ALTER TABLE `formas_pagamento`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`);

--
-- Índices de tabela `hospedes`
--
ALTER TABLE `hospedes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`);

--
-- Índices de tabela `lancamentos_financeiros`
--
ALTER TABLE `lancamentos_financeiros`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`),
  ADD KEY `reserva_id` (`reserva_id`),
  ADD KEY `categoria_id` (`categoria_id`),
  ADD KEY `usuario_id` (`usuario_id`);

--
-- Índices de tabela `movimentacoes_caixa`
--
ALTER TABLE `movimentacoes_caixa`
  ADD PRIMARY KEY (`id`),
  ADD KEY `caixa_id` (`caixa_id`),
  ADD KEY `lancamento_id` (`lancamento_id`),
  ADD KEY `usuario_id` (`usuario_id`),
  ADD KEY `idx_pousada_id` (`pousada_id`);

--
-- Índices de tabela `planos`
--
ALTER TABLE `planos`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `pousadas`
--
ALTER TABLE `pousadas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cnpj` (`cnpj`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Índices de tabela `reservas`
--
ALTER TABLE `reservas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `hospede_id` (`hospede_id`);

--
-- Índices de tabela `sql_query_history`
--
ALTER TABLE `sql_query_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_pousada` (`user_id`,`pousada_id`),
  ADD KEY `idx_executed_at` (`executed_at` DESC),
  ADD KEY `idx_pousada_executed` (`pousada_id`,`executed_at` DESC);

--
-- Índices de tabela `usuarios`
--
ALTER TABLE `usuarios`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `pousada_id` (`pousada_id`);

--
-- AUTO_INCREMENT para tabelas despejadas
--

--
-- AUTO_INCREMENT de tabela `caixa_diario`
--
ALTER TABLE `caixa_diario`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT de tabela `categorias_financeiras`
--
ALTER TABLE `categorias_financeiras`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT de tabela `contratos`
--
ALTER TABLE `contratos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de tabela `formas_pagamento`
--
ALTER TABLE `formas_pagamento`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT de tabela `hospedes`
--
ALTER TABLE `hospedes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=573;

--
-- AUTO_INCREMENT de tabela `lancamentos_financeiros`
--
ALTER TABLE `lancamentos_financeiros`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=117;

--
-- AUTO_INCREMENT de tabela `movimentacoes_caixa`
--
ALTER TABLE `movimentacoes_caixa`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de tabela `planos`
--
ALTER TABLE `planos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT de tabela `pousadas`
--
ALTER TABLE `pousadas`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT de tabela `reservas`
--
ALTER TABLE `reservas`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=675;

--
-- AUTO_INCREMENT de tabela `sql_query_history`
--
ALTER TABLE `sql_query_history`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de tabela `usuarios`
--
ALTER TABLE `usuarios`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- Restrições para tabelas despejadas
--

--
-- Restrições para tabelas `caixa_diario`
--
ALTER TABLE `caixa_diario`
  ADD CONSTRAINT `caixa_diario_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`),
  ADD CONSTRAINT `caixa_diario_ibfk_2` FOREIGN KEY (`usuario_abertura_id`) REFERENCES `usuarios` (`id`),
  ADD CONSTRAINT `caixa_diario_ibfk_3` FOREIGN KEY (`usuario_fechamento_id`) REFERENCES `usuarios` (`id`);

--
-- Restrições para tabelas `categorias_financeiras`
--
ALTER TABLE `categorias_financeiras`
  ADD CONSTRAINT `categorias_financeiras_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);

--
-- Restrições para tabelas `contratos`
--
ALTER TABLE `contratos`
  ADD CONSTRAINT `contratos_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`),
  ADD CONSTRAINT `contratos_ibfk_2` FOREIGN KEY (`plano_id`) REFERENCES `planos` (`id`);

--
-- Restrições para tabelas `formas_pagamento`
--
ALTER TABLE `formas_pagamento`
  ADD CONSTRAINT `formas_pagamento_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);

--
-- Restrições para tabelas `hospedes`
--
ALTER TABLE `hospedes`
  ADD CONSTRAINT `hospedes_ibfk_2` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);

--
-- Restrições para tabelas `lancamentos_financeiros`
--
ALTER TABLE `lancamentos_financeiros`
  ADD CONSTRAINT `lancamentos_financeiros_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`),
  ADD CONSTRAINT `lancamentos_financeiros_ibfk_2` FOREIGN KEY (`reserva_id`) REFERENCES `reservas` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `lancamentos_financeiros_ibfk_3` FOREIGN KEY (`categoria_id`) REFERENCES `categorias_financeiras` (`id`),
  ADD CONSTRAINT `lancamentos_financeiros_ibfk_4` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Restrições para tabelas `movimentacoes_caixa`
--
ALTER TABLE `movimentacoes_caixa`
  ADD CONSTRAINT `movimentacoes_caixa_ibfk_1` FOREIGN KEY (`caixa_id`) REFERENCES `caixa_diario` (`id`),
  ADD CONSTRAINT `movimentacoes_caixa_ibfk_2` FOREIGN KEY (`lancamento_id`) REFERENCES `lancamentos_financeiros` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `movimentacoes_caixa_ibfk_3` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
  ADD CONSTRAINT `movimentacoes_caixa_ibfk_4` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);

--
-- Restrições para tabelas `reservas`
--
ALTER TABLE `reservas`
  ADD CONSTRAINT `reservas_ibfk_1` FOREIGN KEY (`hospede_id`) REFERENCES `hospedes` (`id`);

--
-- Restrições para tabelas `usuarios`
--
ALTER TABLE `usuarios`
  ADD CONSTRAINT `usuarios_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
