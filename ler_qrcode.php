<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instascan</title>            
    <script type="text/javascript" src="https://rawgit.com/schmich/instascan-builds/master/instascan.min.js" ></script>
	</head>
<body>
    <!--<h1>Escanear QRCode</h1>-->
    <center><video id="preview"></video></center>
    <script>
        let scanner = new Instascan.Scanner(
            {
                video: document.getElementById('preview')
            }
        );
        scanner.addListener('scan', function(content) {
			scanner.stop();
			// Redirecionar para o mapa de UH onde o modal de edição está disponível
            window.location.href = 'index.php?page=mapa_uh';
             

        });
        Instascan.Camera.getCameras().then(cameras => 
        {
            if(cameras.length > 0){
                scanner.start(cameras[1]);
            } else {
                console.error("Não existe câmera no dispositivo!");
            }
        });
    </script>
 </body>
</html>