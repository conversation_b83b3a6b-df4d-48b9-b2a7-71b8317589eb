body {
	background-color: #8e7eec;
	font-family: Arial, sans-serif;
}

.navbar.navbar-expand-lg.navbar-light {
	background-color: #04b5ea !important;
}

.container {
	margin-top: 20px;
}

.form-container {
	max-width: 600px;
	margin: 0 auto;
	padding: 20px;
	border: 1px solid #ccc;
	border-radius: 10px;
}

.form-group {
	margin-bottom: 15px;
}

.form-group label {
	display: block;
	margin-bottom: 5px;
}

.form-group input,
.form-group select {
	width: 100%;
	padding: 8px;
	box-sizing: border-box;
	height: 38px;
}

.form-group button {
	padding: 10px 15px;
	background-color: #4CAF50;
	color: white;
	border: none;
	cursor: pointer;
}



.btn-cancelar {
	background-color: #dc3545 !important;
	/* Força a cor vermelha */
	color: white !important;
	padding: 10px 15px;
	border: none;
	cursor: pointer;
}

.form-group-row {
	display: flex;
	justify-content: space-between;
}

.form-group-row .form-group {
	flex: 1;
	margin-right: 10px;
}

.form-group-row .form-group:last-child {
	margin-right: 0;
}

.navbar .nav-link {
	color: white;
}

.navbar .nav-link:hover {
	color: #f8f9fa;
}

.custom-width {
	width: 30%;
}

/*.only-print{ display: none;}*/
#preview {
	width: 300px;
	height: 300px;
}

/*estilos do calendário para garantir que não haja transparência */
.calendar {
	width: 100%;
	border-collapse: collapse;
	background-color: #ffffff !important;
	/* Força o fundo branco */
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
	/* Adiciona sombra para destacar */
}

.calendar th,
.calendar td {
	border: 1px solid #ddd !important;
	padding: 8px !important;
	text-align: center !important;
	background-color: #ffffff !important;
	/* Força o fundo branco para todas as células */
}

.calendar th {
	background-color: #f2f2f2 !important;
	/* Força o fundo cinza claro para cabeçalhos */
}

.calendar .other-month {
	color: #aaa !important;
	background-color: #f9f9f9 !important;
	/* Força o fundo para dias de outros meses */
}

.calendar .today {
	background-color: #e6f7ff !important;
	/* Força o fundo para o dia atual */
	font-weight: bold !important;
}

/* Adicione um container específico para o calendário com fundo branco */
.calendar-container {
	background-color: #ffffff !important;
	padding: 15px !important;
	border-radius: 8px !important;
	border: 1px solid #ddd !important;
	margin-bottom: 20px !important;
}

/* Estilos para os ícones de status */
.status-icon {
	font-size: 20px;
}

.status-icon.atual {
	color: green;
}

.status-icon.futuro {
	color: blue;
}

.status-icon.anterior {
	color: #ffc107;
}

.status-icon.sem-reserva {
	color: gray;
}

/* Estilos para os botões de ação na tabela */
.btn-acao {
	width: 80px;
	height: 32px;
	padding: 4px 8px;
	font-size: 14px;
	margin: 2px;
	display: inline-block;
	text-align: center;
}

/* Estilos para os botões de filtro */
.filtro-btn {
	margin-right: 5px;
	margin-bottom: 5px;
}

/*Estilo para impressão*/

@media print {
	.no-print {
		display: none !important;
	}

	.only-print {
		display: block;
	}

	.print-only {
		display: block !important;
	}

	#qrcode,
	#qrcode-reserva {
		padding: 15px 15px;
		display: block;
	}

	body {
		background-color: #fff;
		font-family: Arial, sans-serif;
	}

	.container {
		margin-top: -30px;
		border: none;
	}

	.form-container {
		max-width: 800px;
		margin: 1 auto;
		padding: 10px;
	}

	.form-group {
		margin-top: -5px;
		margin-bottom: -10px;
	}

	.form-group label {
		display: block;
		margin-bottom: -12px;
		margin-top: 5px;
	}

	.form-group input,
	.form-group select,
	.form-group textarea {
		font-weight: bold;
		line-height: 1.5;
		background-color: transparent;
		margin-top: 2px;
		width: 100%;
		padding: 2px;
		border: none;
		outline: none;
	}

	.form-group button {
		padding: 10px 15px;
		background-color: #4CAF50;
		color: white;
		border: none;
		cursor: pointer;
	}



	.form-group-row {
		display: flex;
		justify-content: space-between;
	}

	.form-group-row .form-group {
		flex: 1;
		margin-right: 10px;
	}

	.form-group-row .form-group:last-child {
		margin-right: 0;
	}

	.form-group textarea[name="acompanhantes"]::placeholder {
		color: transparent;
	}


}

/* Estilos de tela para elementos de impressão */
@media screen {
	.print-only {
		display: none !important;
	}
}

/* Adicione estes estilos para melhorar a responsividade em telas pequenas */

/* Estilos responsivos para tabelas em dispositivos móveis */
@media screen and (max-width: 767px) {

	/* Transforma a tabela para visualização em cartões em telas pequenas */
	.table-responsive-stack {
		display: block;
	}

	.table-responsive-stack thead {
		display: none;
	}

	.table-responsive-stack tbody tr {
		display: block;
		margin-bottom: 1rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		padding: 0.5rem;
		background-color: #fff;
	}

	.table-responsive-stack td {
		display: block;
		text-align: right;
		padding: 0.5rem;
		border-bottom: 1px solid #eee;
	}

	.table-responsive-stack td:last-child {
		border-bottom: none;
	}

	.table-responsive-stack td:before {
		content: attr(data-label);
		float: left;
		font-weight: bold;
	}

	/* Ajustes específicos para a coluna de ações */
	td[data-label='Ações'] {
		text-align: center !important;
		padding: 10px 0;
	}

	/* Faz os botões de ação ficarem maiores em telas touch para facilitar o toque */
	.action-icon {
		width: 44px;
		height: 44px;
		margin: 4px;
	}

	.action-icon i {
		font-size: 18px;
	}
}

/* Estilos para os ícones de ação na tabela */
.action-icon {
	width: 36px;
	height: 36px;
	margin: 2px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	border-radius: 4px;
}

.action-icon i {
	font-size: 16px;
}

/* Espaçamento para a coluna de ações */
td[data-label='Ações'] {
	white-space: nowrap;
	min-width: 120px;
}

/* Adicionar estilos do mapa_uh.php */
.ocupado {
	background-color: #f8d7da;
}

.livre {
	background-color: #d4edda;
}



.ocupado-manha {
	background-color: #fff3cd;
}

.ocupado-tarde {
	background-color: #fff3cd;
}

.table-responsive {
	overflow-x: auto;
}

.cell-clickable {
	cursor: pointer;
}

/* Display icons side by side without wrapping */
.bi {
	margin-right: 0;
	font-size: 0.9em;
}

/* Make sure the cell content is centered */
td {
	text-align: center;
	vertical-align: middle;
}

/* Optional: Highlight current date */
.current-day {
	background-color: #e0e0e0;
}

/* Estilos específicos para impressão de modais */
@media print {
	.modal-content {
		box-shadow: none !important;
		border: none !important;
	}

	.modal-header,
	.modal-footer {
		display: none !important;
	}

	.modal-body {
		padding: 0 !important;
	}

	/* Ocultar elementos específicos do modal na impressão */
	.btn-close,
	.navigation-buttons {
		display: none !important;
	}
}

/* Estilos para cabeçalho de impressão */
.print-header {
	margin-bottom: 20px;
}

.print-header-container {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 20px;
	margin-bottom: 15px;
}

.print-logo-container {
	flex-shrink: 0;
}

.print-logo {
	max-width: 80px;
	max-height: 80px;
	border: 1px solid #ddd;
	padding: 5px;
	background-color: white;
}

.print-info-container {
	flex-grow: 1;
}

.print-pousada-nome {
	font-size: 18px;
	font-weight: bold;
	margin: 0 0 5px 0;
	color: #000;
}

.print-documento-tipo {
	font-size: 16px;
	font-weight: normal;
	margin: 0;
	color: #666;
}

.print-separator {
	border: none;
	border-top: 2px solid #000;
	margin: 15px 0;
}
}