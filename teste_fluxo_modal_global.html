<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Fluxo Modal Global</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .debug-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 500px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
        }
        .log-entry {
            margin-bottom: 2px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .step-indicator {
            background-color: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🔬 Teste Completo do Fluxo Modal Global</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>🎯 Simulação do Fluxo Real</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <span class="step-indicator">1</span>
                            <button class="btn btn-primary" onclick="simularFluxoCompleto()">
                                🚀 Simular Fluxo Completo do Modal
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <span class="step-indicator">2</span>
                            <button class="btn btn-info" onclick="carregarFormularioAjax()">
                                📥 Carregar Formulário via AJAX
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <span class="step-indicator">3</span>
                            <button class="btn btn-success" onclick="configurarValidacoes()">
                                ⚙️ Configurar Validações
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <span class="step-indicator">4</span>
                            <button class="btn btn-warning" onclick="testarValidacoesCPF()">
                                🧪 Testar Validações CPF
                            </button>
                        </div>
                        
                        <hr>
                        
                        <div class="mb-3">
                            <h6>Testes Específicos:</h6>
                            <button class="btn btn-sm btn-danger me-2" onclick="testarCPFEspecifico('069.187.238-46', 'inválido')">CPF Inválido</button>
                            <button class="btn btn-sm btn-warning me-2" onclick="testarCPFEspecifico('069.187.23', 'incompleto')">CPF Incompleto</button>
                            <button class="btn btn-sm btn-info me-2" onclick="testarCPFEspecifico('111.444.777-35', 'válido')">CPF Válido</button>
                        </div>
                    </div>
                </div>
                
                <!-- Container do Modal Simulado -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6>📋 Container do Formulário</h6>
                    </div>
                    <div class="card-body">
                        <div id="modalGlobalNovoHospedeContainer">
                            <div class="text-center text-muted">
                                Aguardando carregamento do formulário...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="debug-panel">
                    <h6>🔍 Debug Log:</h6>
                    <div id="debug-output">
                        <div class="log-entry log-info">Sistema iniciado...</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>📊 Status Atual:</h6>
                    <div id="status-atual" class="alert alert-info">
                        Aguardando início dos testes...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // Sistema de debug
    const debugOutput = document.getElementById('debug-output');
    const statusAtual = document.getElementById('status-atual');
    const container = document.getElementById('modalGlobalNovoHospedeContainer');

    function addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
        debugOutput.appendChild(logEntry);
        debugOutput.scrollTop = debugOutput.scrollHeight;
    }

    function updateStatus(message, type = 'info') {
        statusAtual.className = `alert alert-${type}`;
        statusAtual.innerHTML = message;
    }

    // Simulação do HTML do formulário (igual ao real)
    const formularioHTML = `
        <div class="form-container">
            <center><br><h3>Cadastrar Novo Hóspede</h3></center>
            
            <form id="formHospedeCompleto" action="hospedes_salvar.php" method="POST">
                <input type="hidden" name="acao" value="cadastrar">
                <input type="hidden" name="ajax" value="1">
                
                <div class="form-group">
                    <label for="nome">Nome:</label>
                    <input type="text" name="nome" value="" required>
                </div>
                
                <div class="form-group-row">
                    <div class="form-group">
                        <label for="nasc">Data de Nasc:</label>
                        <input type="date" name="nasc" value="">
                    </div>    
                    <div class="form-group">
                        <label for="idade">Idade:</label>
                        <input type="text" name="idade" value="" readonly>
                    </div>
                </div>   
                        
                <div class="form-group-row">
                    <div class="form-group">
                        <label for="sexo">Sexo:</label>
                        <input type="text" name="sexo" value="">
                    </div>                
                    <div class="form-group">
                        <label for="cpf">CPF:</label>
                        <input type="text" name="cpf" value="">
                        <div id="cpf-feedback" style="font-size: 0.875em; margin-top: 0.25rem; display: block; min-height: 1.2em;">
                            <!-- Feedback será preenchido via JavaScript -->
                        </div>
                    </div>    
                </div>
                
                <div class="form-group">
                    <center>
                    <button type="submit" class="btn btn-primary">Registrar</button>
                    </center>
                </div>
            </form>
        </div>
    `;

    // Função de validação CPF
    function validarCPF(cpf) {
        cpf = cpf.replace(/[^\d]+/g, '');
        if (cpf === '') return false;

        if (cpf.length !== 11 ||
            cpf === "00000000000" ||
            cpf === "11111111111" ||
            cpf === "22222222222" ||
            cpf === "33333333333" ||
            cpf === "44444444444" ||
            cpf === "55555555555" ||
            cpf === "66666666666" ||
            cpf === "77777777777" ||
            cpf === "88888888888" ||
            cpf === "99999999999")
            return false;

        let add = 0;
        for (let i = 0; i < 9; i++)
            add += parseInt(cpf.charAt(i)) * (10 - i);
        let rev = 11 - (add % 11);
        if (rev === 10 || rev === 11)
            rev = 0;
        if (rev !== parseInt(cpf.charAt(9)))
            return false;

        add = 0;
        for (let i = 0; i < 10; i++)
            add += parseInt(cpf.charAt(i)) * (11 - i);
        rev = 11 - (add % 11);
        if (rev === 10 || rev === 11)
            rev = 0;
        if (rev !== parseInt(cpf.charAt(10)))
            return false;

        return true;
    }

    // Simular fluxo completo
    function simularFluxoCompleto() {
        addLog('🚀 Iniciando simulação do fluxo completo...', 'info');
        updateStatus('Simulando fluxo completo...', 'warning');
        
        setTimeout(() => carregarFormularioAjax(), 500);
    }

    // Simular carregamento via AJAX
    function carregarFormularioAjax() {
        addLog('📥 Simulando carregamento via AJAX...', 'info');
        updateStatus('Carregando formulário via AJAX...', 'warning');
        
        // Simular delay do AJAX
        setTimeout(() => {
            addLog('✅ HTML recebido do servidor', 'success');
            container.innerHTML = formularioHTML;
            addLog('✅ HTML inserido no container', 'success');
            
            setTimeout(() => configurarValidacoes(), 300);
        }, 1000);
    }

    // Configurar validações (igual ao sistema real)
    function configurarValidacoes() {
        addLog('⚙️ Iniciando configuração de validações...', 'info');
        updateStatus('Configurando validações...', 'warning');
        
        const form = container.querySelector('form');
        addLog(`🔍 Formulário encontrado: ${form ? 'SIM' : 'NÃO'}`, form ? 'success' : 'error');
        
        if (!form) {
            addLog('❌ Erro: Formulário não encontrado!', 'error');
            updateStatus('Erro: Formulário não encontrado!', 'danger');
            return;
        }

        const cpfField = form.querySelector('input[name="cpf"]');
        addLog(`🔍 Campo CPF encontrado: ${cpfField ? 'SIM' : 'NÃO'}`, cpfField ? 'success' : 'error');
        
        if (!cpfField) {
            addLog('❌ Erro: Campo CPF não encontrado!', 'error');
            updateStatus('Erro: Campo CPF não encontrado!', 'danger');
            return;
        }

        let feedbackDiv = document.getElementById('cpf-feedback');
        addLog(`🔍 Div feedback encontrado: ${feedbackDiv ? 'SIM' : 'NÃO'}`, feedbackDiv ? 'success' : 'error');
        
        if (!feedbackDiv) {
            addLog('🔧 Criando div feedback...', 'warning');
            feedbackDiv = document.createElement('div');
            feedbackDiv.id = 'cpf-feedback';
            feedbackDiv.style.fontSize = '0.875em';
            feedbackDiv.style.marginTop = '0.25rem';
            feedbackDiv.style.display = 'block';
            feedbackDiv.style.minHeight = '1.2em';
            cpfField.parentNode.appendChild(feedbackDiv);
        }

        addLog('🔧 Adicionando event listener...', 'info');
        cpfField.addEventListener('input', function() {
            addLog(`🎯 Event listener executado: "${this.value}"`, 'info');
            
            if (!this.value) {
                this.value = '';
                return;
            }
            
            // Formatação
            let cpf = this.value.replace(/\D/g, '');
            if (cpf.length <= 11) {
                cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                cpf.replace(/(\d{3})(\d)/, '$1.$2');
                cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            }
            this.value = cpf;

            // Validação
            if (cpf.length < 14 && cpf.length > 0) {
                addLog('⚠️ CPF incompleto detectado', 'warning');
                feedbackDiv.innerHTML = '<b>CPF incompleto</b>. Digite todos os dígitos.';
                feedbackDiv.style.color = 'orange';
                updateStatus('CPF incompleto detectado', 'warning');
            } else if (cpf.length === 14) {
                if (validarCPF(cpf)) {
                    addLog('✅ CPF válido detectado', 'success');
                    feedbackDiv.innerHTML = '<b>CPF válido</b> - verificando disponibilidade...';
                    feedbackDiv.style.color = 'green';
                    updateStatus('CPF válido detectado', 'success');
                } else {
                    addLog('❌ CPF inválido detectado', 'error');
                    feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
                    feedbackDiv.style.color = 'red';
                    updateStatus('CPF inválido detectado', 'danger');
                }
            } else if (cpf.length === 0) {
                feedbackDiv.innerHTML = '';
                updateStatus('Campo CPF vazio', 'info');
            }
        });
        
        addLog('✅ Event listener configurado com sucesso!', 'success');
        updateStatus('Validações configuradas com sucesso!', 'success');
        
        setTimeout(() => testarValidacoesCPF(), 1000);
    }

    // Testar validações automaticamente
    function testarValidacoesCPF() {
        addLog('🧪 Iniciando testes automáticos...', 'info');
        
        setTimeout(() => testarCPFEspecifico('069.187.23', 'incompleto'), 1000);
        setTimeout(() => testarCPFEspecifico('069.187.238-46', 'inválido'), 3000);
        setTimeout(() => testarCPFEspecifico('111.444.777-35', 'válido'), 5000);
    }

    // Testar CPF específico
    function testarCPFEspecifico(cpf, tipo) {
        addLog(`🧪 Testando CPF ${tipo}: ${cpf}`, 'info');
        
        const cpfField = container.querySelector('input[name="cpf"]');
        if (cpfField) {
            cpfField.value = cpf;
            cpfField.dispatchEvent(new Event('input', { bubbles: true }));
            
            setTimeout(() => {
                const feedbackDiv = document.getElementById('cpf-feedback');
                if (feedbackDiv && feedbackDiv.innerHTML.trim()) {
                    addLog(`✅ Feedback recebido para CPF ${tipo}: ${feedbackDiv.innerHTML}`, 'success');
                } else {
                    addLog(`❌ Nenhum feedback para CPF ${tipo}`, 'error');
                }
            }, 500);
        } else {
            addLog('❌ Campo CPF não encontrado para teste!', 'error');
        }
    }

    // Inicializar
    document.addEventListener('DOMContentLoaded', function() {
        addLog('📋 Página de teste carregada', 'info');
        updateStatus('Sistema pronto para testes', 'success');
    });
    </script>
</body>
</html>
