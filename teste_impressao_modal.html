<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Impressão Modal Reserva</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="custom/css/form_fnrh.css">
    <style>
        /* Estilos de demonstração */
        .demo-container {
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .demo-title {
            color: #28a745;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">Teste - Funcionalidade de Impressão Modal Reserva</h1>
        
        <div class="demo-container">
            <h3 class="demo-title">Demonstração da Implementação</h3>
            <p><strong>Funcionalidades implementadas:</strong></p>
            <ul>
                <li>✅ Botão de impressão no modal de editar reserva</li>
                <li>✅ QR Code gerado com ID do hóspede</li>
                <li>✅ Logo da pousada visível apenas na impressão</li>
                <li>✅ Estilos CSS específicos para impressão</li>
                <li>✅ Carregamento dinâmico da biblioteca QR Code</li>
            </ul>
            
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exemploModal">
                Abrir Modal de Exemplo
            </button>
        </div>
        
        <div class="demo-container">
            <h3 class="demo-title">Arquivos Modificados</h3>
            <ul>
                <li><code>carregar_formulario_edicao.php</code> - Adicionado elementos de impressão e QR code</li>
                <li><code>custom/js/modal-editar-reserva.js</code> - Implementada funcionalidade de impressão</li>
                <li><code>custom/css/form_fnrh.css</code> - Adicionados estilos para impressão de modais</li>
            </ul>
        </div>
    </div>

    <!-- Modal de Exemplo -->
    <div class="modal fade" id="exemploModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Exemplo - Editar Reserva</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Cabeçalho para impressão (início da página) -->
                    <div class="print-header print-only">
                        <div class="print-header-container">
                            <div class="print-logo-container">
                                <img src="img/Logo_Bom_Viver.png" alt="Logo" class="print-logo">
                            </div>
                            <div class="print-info-container">
                                <h2 class="print-pousada-nome">Pousada Bom Viver</h2>
                                <h3 class="print-documento-tipo">Reserva de Unidade Habitacional</h3>
                            </div>
                        </div>
                        <hr class="print-separator">
                    </div>

                    <!-- Formulário de exemplo -->
                    <div class="form-container">
                        <center><br><h3>
                            <b>João da Silva</b>
                        </h3></center>

                        <form id="formEditarReserva">
                            <div class="form-group-row">
                                <div class="form-group">
                                    <label for="uh">Un. Habit.</label>
                                    <input type="text" name="uh" value="101" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="numacomp">Nº Acomp.</label>
                                    <input type="number" name="numacomp" value="2">
                                </div>
                                <div class="form-group">
                                    <label for="valor">Valor</label>
                                    <input type="text" name="valor" value="R$ 150,00">
                                </div>
                            </div>
                            
                            <div class="form-group-row">
                                <div class="form-group">
                                    <label for="dataentrada">Entrada: Data</label>
                                    <input type="date" name="dataentrada" value="2025-01-20">
                                </div>
                                <div class="form-group">
                                    <label for="horaentrada">Hora</label>
                                    <input type="time" name="horaentrada" value="14:00">
                                </div>
                            </div>
                            
                            <div class="form-group-row">
                                <div class="form-group">
                                    <label for="datasaida">Saída: Data</label>
                                    <input type="date" name="datasaida" value="2025-01-22">
                                </div>
                                <div class="form-group">
                                    <label for="horasaida">Hora</label>
                                    <input type="time" name="horasaida" value="12:00">
                                </div>
                            </div>
                        </form>

                        <!-- Botão de impressão -->
                        <div class="form-group no-print mt-3">
                            <button type="button" class="btn btn-secondary" id="print-button-reserva">
                                <i class="bi bi-printer"></i> Imprimir Reserva
                            </button>
                        </div>

                        <!-- QR Code -->
                        <center class="print-only">
                            <br>
                            <div id="qrcode-reserva"></div>
                            <br>
                            <p><small>QR Code do Hóspede: 123</small></p>
                        </center>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-primary">Salvar Alterações</button>
                </div>
            </div>
        </div>
    </div>

    <script src="css/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="instascan/qrcode.js"></script>
    <script>
        // Simular a funcionalidade implementada
        document.addEventListener('DOMContentLoaded', function() {
            const printButton = document.getElementById('print-button-reserva');
            if (printButton) {
                printButton.addEventListener('click', function() {
                    window.print();
                });
            }

            // Criar QR Code de exemplo
            const qrContainer = document.getElementById('qrcode-reserva');
            if (qrContainer && typeof QRCode !== 'undefined') {
                const qrcode = new QRCode(qrContainer, {
                    text: "123", // ID do hóspede de exemplo
                    width: 80,
                    height: 80,
                    colorDark: "black",
                    colorLight: "white",
                    correctLevel: QRCode.CorrectLevel.H
                });
            }
        });
    </script>
</body>
</html>
