<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Validação CPF Modal Global</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-case {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .console-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">Teste de Validação CPF - Modal Global</h1>
        
        <div class="alert alert-info">
            <h5>Objetivo do Teste:</h5>
            <p>Verificar se as três situações de validação de CPF estão funcionando no modal global:</p>
            <ol>
                <li><strong>CPF Inválido:</strong> Deve mostrar "CPF inválido"</li>
                <li><strong>CPF Incompleto:</strong> Deve mostrar "CPF incompleto. Digite todos os dígitos"</li>
                <li><strong>CPF Duplicado:</strong> Deve mostrar "CPF já cadastrado para: [nome]"</li>
            </ol>
        </div>

        <div class="test-case">
            <h5>Teste 1: CPF Inválido</h5>
            <p><strong>CPF para testar:</strong> 069.187.238-46 (inválido)</p>
            <button class="btn btn-danger btn-sm" onclick="testarCPF('069.187.238-46', 'inválido')">Testar CPF Inválido</button>
            <div id="resultado-invalido" class="mt-2"></div>
        </div>

        <div class="test-case">
            <h5>Teste 2: CPF Incompleto</h5>
            <p><strong>CPF para testar:</strong> 069.187.23 (incompleto)</p>
            <button class="btn btn-warning btn-sm" onclick="testarCPF('069.187.23', 'incompleto')">Testar CPF Incompleto</button>
            <div id="resultado-incompleto" class="mt-2"></div>
        </div>

        <div class="test-case">
            <h5>Teste 3: CPF Duplicado</h5>
            <p><strong>CPF para testar:</strong> 069.187.238-47 (pode estar cadastrado)</p>
            <button class="btn btn-info btn-sm" onclick="testarCPF('069.187.238-47', 'duplicado')">Testar CPF Duplicado</button>
            <div id="resultado-duplicado" class="mt-2"></div>
        </div>

        <div class="test-case">
            <h5>Abrir Modal Global</h5>
            <p>Teste manual no modal real:</p>
            <button class="btn btn-primary" onclick="abrirModalNovoHospedeGlobal()">
                <i class="bi bi-person-plus"></i> Abrir Modal Global
            </button>
        </div>

        <div class="console-log">
            <h6>Console Log:</h6>
            <div id="console-output">
                <div class="log-entry log-info">Aguardando testes...</div>
            </div>
        </div>
    </div>

    <!-- Modal Global Simulado -->
    <div class="modal fade" id="modalGlobalNovoHospede" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cadastrar Novo Hóspede</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalGlobalNovoHospedeContainer">
                    <form id="formHospedeCompleto">
                        <div class="form-group mb-3">
                            <label for="nome">Nome:</label>
                            <input type="text" name="nome" class="form-control" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="cpf">CPF:</label>
                            <input type="text" name="cpf" class="form-control">
                            <div id="cpf-feedback"></div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <button type="submit" class="btn btn-primary">Registrar</button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // Interceptar console.log
    const originalLog = console.log;
    const outputDiv = document.getElementById('console-output');

    function addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
        outputDiv.appendChild(logEntry);
        outputDiv.scrollTop = outputDiv.scrollHeight;
    }

    console.log = function(...args) {
        const message = args.join(' ');
        if (message.includes('CPF')) {
            if (message.includes('inválido')) {
                addLog(message, 'error');
            } else if (message.includes('incompleto')) {
                addLog(message, 'warning');
            } else if (message.includes('duplicado') || message.includes('cadastrado')) {
                addLog(message, 'error');
            } else if (message.includes('disponível') || message.includes('válido')) {
                addLog(message, 'success');
            } else {
                addLog(message, 'info');
            }
        }
        originalLog.apply(console, args);
    };

    // Função para testar CPF específico
    function testarCPF(cpf, tipo) {
        addLog(`Iniciando teste de CPF ${tipo}: ${cpf}`, 'info');
        
        const modal = new bootstrap.Modal(document.getElementById('modalGlobalNovoHospede'));
        modal.show();
        
        setTimeout(() => {
            const cpfField = document.querySelector('input[name="cpf"]');
            if (cpfField) {
                addLog(`Campo CPF encontrado, inserindo valor: ${cpf}`, 'info');
                cpfField.value = cpf;
                cpfField.dispatchEvent(new Event('input', { bubbles: true }));
                
                setTimeout(() => {
                    const feedbackDiv = document.getElementById('cpf-feedback');
                    if (feedbackDiv && feedbackDiv.innerHTML) {
                        addLog(`Feedback recebido: ${feedbackDiv.innerHTML}`, 'success');
                        document.getElementById(`resultado-${tipo}`).innerHTML = 
                            `<div class="alert alert-success">✅ Feedback: ${feedbackDiv.innerHTML}</div>`;
                    } else {
                        addLog(`Nenhum feedback encontrado para CPF ${tipo}`, 'error');
                        document.getElementById(`resultado-${tipo}`).innerHTML = 
                            `<div class="alert alert-danger">❌ Nenhum feedback encontrado</div>`;
                    }
                }, 1000);
            } else {
                addLog('Campo CPF não encontrado!', 'error');
            }
        }, 500);
    }

    // Função simulada para abrir modal
    function abrirModalNovoHospedeGlobal() {
        addLog('Abrindo modal global para teste manual...', 'info');
        const modal = new bootstrap.Modal(document.getElementById('modalGlobalNovoHospede'));
        modal.show();
    }

    // Log inicial
    document.addEventListener('DOMContentLoaded', function() {
        addLog('Página de teste carregada. Pronto para testes.', 'info');
    });
    </script>
</body>
</html>
