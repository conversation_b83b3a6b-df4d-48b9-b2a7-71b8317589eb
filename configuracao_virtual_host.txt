# CONFIGURAÇÃO VIRTUAL HOST PARA XAMPP
# Para usar http://pousada.local em vez de http://localhost/pousada

## 1. EDITAR ARQUIVO HOSTS
# Arquivo: C:\Windows\System32\drivers\etc\hosts
# Adicionar esta linha no final:
127.0.0.1 pousada.local

## 2. CONFIGURAR APACHE (httpd-vhosts.conf)
# Arquivo: C:\xampp\apache\conf\extra\httpd-vhosts.conf
# Adicionar no final:

<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/pousada"
    ServerName pousada.local
    ServerAlias www.pousada.local
    
    <Directory "C:/xampp/htdocs/pousada">
        AllowOverride All
        Require all granted
    </Directory>
    
    # Logs para debug
    ErrorLog "logs/pousada.local-error.log"
    CustomLog "logs/pousada.local-access.log" common
</VirtualHost>

# Virtual host para localhost (manter funcionando)
<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs"
    ServerName localhost
</VirtualHost>

## 3. HABILITAR VIRTUAL HOSTS
# Arquivo: C:\xampp\apache\conf\httpd.conf
# Descomentar esta linha (remover #):
# Include conf/extra/httpd-vhosts.conf

## 4. REINICIAR APACHE
# No XAMPP Control Panel: Stop → Start Apache

## 5. TESTAR
# Acesse: http://pousada.local
# Deve carregar sua aplicação

## 6. ATUALIZAR config_local.php
# Adicionar detecção para pousada.local:

$isLocal = (
    $_SERVER['HTTP_HOST'] === 'localhost' || 
    $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
    $_SERVER['HTTP_HOST'] === 'pousada.local' ||
    strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
);
