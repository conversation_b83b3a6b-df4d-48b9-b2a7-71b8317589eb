<?php
// Script para corrigir collation do arquivo SQL para compatibilidade com XAMPP

$inputFile = 'claudio2_pousada.sql';
$outputFile = 'claudio2_pousada_fixed.sql';

echo "🔧 Corrigindo collation do arquivo SQL...\n";

// Ler o arquivo original
$content = file_get_contents($inputFile);

if ($content === false) {
    die("❌ Erro: Não foi possível ler o arquivo $inputFile\n");
}

echo "📁 Arquivo lido: " . strlen($content) . " bytes\n";

// Substituições para compatibilidade
$replacements = [
    // Collation principal
    'utf8mb4_0900_ai_ci' => 'utf8mb4_general_ci',
    
    // Engine se necessário
    'ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci' => 'ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci',
    
    // Collation específica em campos
    'CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci' => 'CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci',
    
    // Outras variações possíveis
    'COLLATE=utf8mb4_0900_ai_ci' => 'COLLATE=utf8mb4_general_ci',
    'COLLATE utf8mb4_0900_ai_ci' => 'COLLATE utf8mb4_general_ci'
];

// Aplicar substituições
$originalContent = $content;
foreach ($replacements as $search => $replace) {
    $count = 0;
    $content = str_replace($search, $replace, $content, $count);
    if ($count > 0) {
        echo "✅ Substituído '$search' → '$replace' ($count vezes)\n";
    }
}

// Verificar se houve mudanças
if ($content === $originalContent) {
    echo "⚠️ Nenhuma substituição foi necessária\n";
} else {
    echo "🔄 Arquivo modificado com sucesso\n";
}

// Salvar arquivo corrigido
$result = file_put_contents($outputFile, $content);

if ($result === false) {
    die("❌ Erro: Não foi possível salvar o arquivo $outputFile\n");
}

echo "💾 Arquivo salvo: $outputFile (" . strlen($content) . " bytes)\n";
echo "✅ Correção concluída!\n\n";

echo "📋 PRÓXIMOS PASSOS:\n";
echo "1. Use o arquivo: $outputFile\n";
echo "2. Importe no phpMyAdmin\n";
echo "3. Deve funcionar sem erros de collation\n";

?>
