<?php
// Configuração para ambiente local (XAMPP)

// Detectar se está rodando localmente
$isLocal = (
    $_SERVER['HTTP_HOST'] === 'localhost' ||
    $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
    $_SERVER['HTTP_HOST'] === 'pousada.local' ||
    strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0 ||
    strpos($_SERVER['HTTP_HOST'], '127.0.0.1:') === 0
);

if ($isLocal) {
    // Configurações locais (XAMPP)
    $host = 'localhost';
    $username = 'root';
    $password = ''; // XAMPP vem sem senha por padrão
    $database = 'claudio2_pousada'; // Nome do banco importado
    
    echo "<!-- Ambiente: LOCAL -->\n";
} else {
    // Configurações do servidor (suas originais)
    $host = 'seu_host_servidor';
    $username = 'seu_usuario_servidor';
    $password = 'sua_senha_servidor';
    $database = 'seu_banco_servidor';
    
    echo "<!-- Ambiente: SERVIDOR -->\n";
}

// Criar conexão
$conn = new mysqli($host, $username, $password, $database);

// Verificar conexão
if ($conn->connect_error) {
    die("Erro de conexão: " . $conn->connect_error);
}

// Configurar charset
$conn->set_charset("utf8mb4");

// Variáveis globais para compatibilidade
$GLOBALS['conn'] = $conn;

// Função para debug (só funciona localmente)
function debugLocal($message) {
    global $isLocal;
    if ($isLocal) {
        echo "<script>console.log('🔍 DEBUG: " . addslashes($message) . "');</script>\n";
    }
}

// Função para preparar e executar queries (compatibilidade)
function prepareAndExecute($conn, $sql, $types = '', ...$params) {
    $stmt = $conn->prepare($sql);
    if ($types && !empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    return $stmt;
}

debugLocal("Conexão com banco estabelecida - Ambiente: " . ($isLocal ? 'LOCAL' : 'SERVIDOR'));
?>
