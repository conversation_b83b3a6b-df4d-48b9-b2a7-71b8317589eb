var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
// Módulo para gerenciar o modal de detalhes das reservas
class ModalDetalhes {
    constructor() {
        this.currentUh = null;
        this.currentData = null;
        this.datasDisponiveis = [];
        this.reservaModal = document.getElementById('reservaModal');
        this.init();
    }
    init() {
        if (this.reservaModal) {
            this.reservaModal.addEventListener('show.bs.modal', (event) => {
                if (event.relatedTarget) {
                    this.handleModalShow(event);
                }
                else {
                    console.error('Evento do modal não possui relatedTarget');
                }
            });
        }
        this.inicializarBotoesNavegacao();
    }
    inicializarBotoesNavegacao() {
        const btnAnterior = document.getElementById('btnAnterior');
        const btnProximo = document.getElementById('btnProximo');
        if (btnAnterior) {
            btnAnterior.addEventListener('click', () => this.navegarAnterior());
        }
        if (btnProximo) {
            btnProximo.addEventListener('click', () => this.navegarProximo());
        }
    }
    handleModalShow(event) {
        const cell = event.relatedTarget;
        const uh = cell.getAttribute('data-uh');
        const data = cell.getAttribute('data-data');
        const cellClass = cell.className;
        // Armazenar dados atuais para navegação
        this.currentUh = uh;
        this.currentData = data;
        this.mapearDatasDisponiveis();
        const ehDataPassada = cellClass.includes('data-passada');
        this.setBasicInfo(uh, data);
        this.resetDisplay();
        // Buscar todas as reservas do dia para esta UH
        this.buscarReservasDodia(uh, data).then(() => {
            this.setupNovaReservaButton(uh, data, cellClass, ehDataPassada);
            this.atualizarEstadoBotoes();
        }).catch(error => {
            console.error('Erro ao buscar reservas do dia:', error);
            this.setupNovaReservaButton(uh, data, cellClass, ehDataPassada);
            this.atualizarEstadoBotoes();
        });
    }
    setBasicInfo(uh, data) {
        const modalUhElement = document.getElementById('modalUh');
        const modalDataElement = document.getElementById('modalData');
        if (modalUhElement)
            modalUhElement.textContent = uh || 'N/A';
        if (modalDataElement) {
            modalDataElement.textContent = data || 'N/A';
            // Destacar a data atual no topo
            this.destacarDataAtual(modalDataElement, data);
        }
    }
    resetDisplay() {
        const elements = [
            'reservaInfo', 'reservasLista', 'novaReservaLink'
        ];
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element)
                element.style.display = 'none';
        });
    }
    buscarReservasDodia(uh, data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const formData = new FormData();
                formData.append('uh', uh || '');
                formData.append('data', data || '');
                const response = yield fetch('custom/includes/buscar_reservas_data.php', {
                    method: 'POST',
                    body: formData
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const resultado = yield response.json();
                if (resultado.sucesso && resultado.reservas && resultado.reservas.length > 0) {
                    this.criarListaReservas(resultado.reservas);
                    // Destacar datas após criar a lista
                    setTimeout(() => this.destacarDatasNasReservas(), 100);
                }
                return resultado;
            }
            catch (error) {
                console.error('Erro ao buscar reservas:', error);
                throw error;
            }
        });
    }
    criarListaReservas(reservas) {
        if (!reservas || reservas.length === 0) {
            return;
        }
        const reservasLista = document.getElementById('reservasLista');
        const reservasContainer = document.getElementById('reservasContainer');
        if (!reservasLista || !reservasContainer) {
            console.error('Elementos da lista de reservas não encontrados');
            return;
        }
        // Limpar lista existente
        reservasContainer.innerHTML = '';
        // Criar linha para cada reserva
        reservas.forEach((reserva) => {
            const reservaDiv = document.createElement('div');
            reservaDiv.className = 'border rounded p-3 mb-2 bg-light';
            // Processar acompanhantes
            let acompanhantesInfo = '';
            if (reserva.numacomp && reserva.numacomp > 0) {
                if (reserva.acompanhantes && reserva.acompanhantes.trim() !== '') {
                    acompanhantesInfo = `<strong>Acompanhantes (${reserva.numacomp}):</strong> ${reserva.acompanhantes}`;
                }
                else {
                    acompanhantesInfo = `<strong>Acompanhantes:</strong> ${reserva.numacomp}`;
                }
            }
            else {
                acompanhantesInfo = '<strong>Acompanhantes:</strong> Nenhum';
            }
            reservaDiv.innerHTML = `
        <div class="row">
          <div class="col-md-8">
            <h6 class="mb-2">${reserva.hospede_nome || 'Não informado'}</h6>
            <p class="mb-1"><strong>Entrada:</strong> <span class="data-entrada">${reserva.dataentrada_formatada}</span> ${reserva.horaentrada_formatada ? 'às ' + reserva.horaentrada_formatada : ''}</p>
            <p class="mb-1"><strong>Saída:</strong> <span class="data-saida">${reserva.datasaida_formatada}</span> ${reserva.horasaida_formatada ? 'às ' + reserva.horasaida_formatada : ''}</p>
            <p class="mb-0">${acompanhantesInfo}</p>
          </div>
          <div class="col-md-4 d-flex align-items-center justify-content-end">
            <div class="btn-group" role="group">
              <button class="btn btn-primary btn-sm" onclick="editarReserva(${reserva.id}, '${reserva.hospede_nome || ''}')">Editar</button>
              <button class="btn btn-danger btn-sm" onclick="excluirReserva(${reserva.id}, '${reserva.hospede_nome || ''}')">Excluir</button>
            </div>
          </div>
        </div>
      `;
            // Adicionar classe para identificar a reserva
            reservaDiv.classList.add('reserva-item');
            reservasContainer.appendChild(reservaDiv);
        });
        // Mostrar a lista
        reservasLista.style.display = 'block';
    }
    setupNovaReservaButton(uh, data, cellClass, ehDataPassada) {
        const novaReservaLink = document.getElementById('novaReservaLink');
        if (!novaReservaLink)
            return;
        // Remover event listeners anteriores
        novaReservaLink.replaceWith(novaReservaLink.cloneNode(true));
        const novaReservaLinkAtualizado = document.getElementById('novaReservaLink');
        if (!novaReservaLinkAtualizado)
            return;
        // Sempre mostrar botão de nova reserva (sem limite de reservas)
        if (true) {
            if (ehDataPassada) {
                // Para reserva retroativa, usar o mesmo modal que reservas normais
                novaReservaLinkAtualizado.removeAttribute('href');
                novaReservaLinkAtualizado.setAttribute('href', '#');
                if (uh)
                    novaReservaLinkAtualizado.setAttribute('data-uh', uh);
                if (data)
                    novaReservaLinkAtualizado.setAttribute('data-data', data);
                novaReservaLinkAtualizado.textContent = 'Reserva Retroativa';
                novaReservaLinkAtualizado.className = 'btn btn-danger';
            }
            else {
                novaReservaLinkAtualizado.removeAttribute('href');
                novaReservaLinkAtualizado.setAttribute('href', '#');
                if (uh)
                    novaReservaLinkAtualizado.setAttribute('data-uh', uh);
                if (data)
                    novaReservaLinkAtualizado.setAttribute('data-data', data);
                novaReservaLinkAtualizado.textContent = 'Nova Reserva';
                novaReservaLinkAtualizado.className = 'btn btn-success';
            }
            novaReservaLinkAtualizado.style.display = 'inline-block';
        }
    }
    mapearDatasDisponiveis() {
        if (!this.currentUh)
            return;
        // Buscar todas as células da mesma UH
        const celulasUh = document.querySelectorAll(`td[data-uh="${this.currentUh}"]`);
        this.datasDisponiveis = Array.from(celulasUh)
            .map(cell => cell.getAttribute('data-data'))
            .filter(data => data !== null)
            .sort();
        this.atualizarEstadoBotoes();
    }
    navegarAnterior() {
        if (!this.currentUh || !this.currentData)
            return;
        const currentIndex = this.datasDisponiveis.indexOf(this.currentData);
        if (currentIndex > 0) {
            const novaData = this.datasDisponiveis[currentIndex - 1];
            this.navegarParaData(novaData);
        }
    }
    navegarProximo() {
        if (!this.currentUh || !this.currentData)
            return;
        const currentIndex = this.datasDisponiveis.indexOf(this.currentData);
        if (currentIndex < this.datasDisponiveis.length - 1) {
            const novaData = this.datasDisponiveis[currentIndex + 1];
            this.navegarParaData(novaData);
        }
    }
    navegarParaData(novaData) {
        // Encontrar a célula correspondente
        const novaCelula = document.querySelector(`td[data-uh="${this.currentUh}"][data-data="${novaData}"]`);
        if (novaCelula) {
            // Simular clique na nova célula
            this.currentData = novaData;
            // Recarregar dados do modal
            const uh = novaCelula.getAttribute('data-uh');
            const data = novaCelula.getAttribute('data-data');
            const cellClass = novaCelula.className;
            this.carregarDadosModal(uh, data, cellClass);
            this.atualizarEstadoBotoes();
        }
    }
    atualizarEstadoBotoes() {
        const btnAnterior = document.getElementById('btnAnterior');
        const btnProximo = document.getElementById('btnProximo');
        if (!this.currentData || !btnAnterior || !btnProximo)
            return;
        const currentIndex = this.datasDisponiveis.indexOf(this.currentData);
        btnAnterior.disabled = currentIndex <= 0;
        btnProximo.disabled = currentIndex >= this.datasDisponiveis.length - 1;
    }
    carregarDadosModal(uh, data, cellClass) {
        // Reutilizar a lógica existente do handleModalShow
        this.setBasicInfo(uh, data);
        this.resetDisplay();
        this.buscarReservasDodia(uh, data).then(() => {
            // Verificar se é data passada
            const ehDataPassada = cellClass.includes('data-passada');
            this.setupNovaReservaButton(uh, data, cellClass, ehDataPassada);
        }).catch(error => {
            console.error('Erro ao buscar reservas do dia:', error);
            const ehDataPassada = cellClass.includes('data-passada');
            this.setupNovaReservaButton(uh, data, cellClass, ehDataPassada);
        });
    }
    destacarDataAtual(elemento, data) {
        if (!data)
            return;
        // Aplicar destaque se for a data atual sendo visualizada
        elemento.classList.add('data-atual-destaque');
    }
    destacarDatasNasReservas() {
        if (!this.currentData)
            return;
        // Converter data atual para formato de comparação (dd/mm/yyyy)
        const dataAtualFormatada = this.formatarDataParaExibicao(this.currentData);
        // Destacar datas de entrada e saída que coincidem com a data atual do modal
        const datasEntrada = Array.from(document.querySelectorAll('.reserva-item .data-entrada'));
        const datasSaida = Array.from(document.querySelectorAll('.reserva-item .data-saida'));
        [...datasEntrada, ...datasSaida].forEach(elemento => {
            var _a;
            const textoData = (_a = elemento.textContent) === null || _a === void 0 ? void 0 : _a.trim();
            if (textoData && textoData === dataAtualFormatada) {
                elemento.classList.add('data-atual-destaque');
            }
        });
    }
    formatarDataParaComparacao(dataTexto) {
        // Converter "13/07/2025" para "2025-07-13" para comparação
        const partes = dataTexto.split('/');
        if (partes.length === 3) {
            return `${partes[2]}-${partes[1].padStart(2, '0')}-${partes[0].padStart(2, '0')}`;
        }
        return dataTexto;
    }
    formatarDataParaExibicao(dataISO) {
        // Converter "2025-07-13" para "13/07/2025" para comparação com o texto exibido
        const partes = dataISO.split('-');
        if (partes.length === 3) {
            return `${partes[2].padStart(2, '0')}/${partes[1].padStart(2, '0')}/${partes[0]}`;
        }
        return dataISO;
    }
}
// Funções globais para os botões das reservas
window.editarReserva = function (reservaId, hospedeNome) {
    const reservaModal = bootstrap.Modal.getInstance(document.getElementById('reservaModal'));
    if (reservaModal) {
        reservaModal.hide();
    }
    setTimeout(() => {
        if (window.modalEditarReserva) {
            window.modalEditarReserva.carregarFormularioEdicao(reservaId, hospedeNome || '');
        }
    }, 300);
};
window.excluirReserva = function (reservaId, hospedeNome) {
    if (confirm(`Tem certeza que deseja excluir a reserva de ${hospedeNome || 'este hóspede'}?`)) {
        window.location.href = `index.php?page=reservas_salvar&acao=excluir_mapa&reserva_id=${reservaId}&hospede_nome=${encodeURIComponent(hospedeNome || '')}`;
    }
};
// Exportar como módulo ES6
export { ModalDetalhes };
