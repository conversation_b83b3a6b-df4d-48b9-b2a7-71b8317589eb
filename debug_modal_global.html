<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Modal Global CPF</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        .debug-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 3px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .test-form {
            border: 2px solid #007bff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="text-center mb-4">🔍 Debug Modal Global CPF</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="test-form">
                    <h5>Teste Direto - Formulário Simulado</h5>
                    <form id="testForm">
                        <div class="form-group mb-3">
                            <label for="nome">Nome:</label>
                            <input type="text" name="nome" class="form-control" value="Teste">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="cpf">CPF:</label>
                            <input type="text" name="cpf" class="form-control" placeholder="Digite um CPF para testar">
                            <div id="cpf-feedback" style="font-size: 0.875em; margin-top: 0.25rem; display: block; min-height: 1.2em;">
                                <!-- Feedback será preenchido via JavaScript -->
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <button type="submit" class="btn btn-primary">Registrar</button>
                        </div>
                    </form>
                </div>
                
                <div class="mt-3">
                    <h6>Testes Rápidos:</h6>
                    <button class="btn btn-sm btn-danger me-2" onclick="testarCPF('069.187.238-46')">CPF Inválido</button>
                    <button class="btn btn-sm btn-warning me-2" onclick="testarCPF('069.187.23')">CPF Incompleto</button>
                    <button class="btn btn-sm btn-info me-2" onclick="testarCPF('111.444.777-35')">CPF Válido</button>
                    <button class="btn btn-sm btn-secondary" onclick="limparCPF()">Limpar</button>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="abrirModalReal()">
                        🚀 Abrir Modal Real do Sistema
                    </button>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="debug-log">
                    <h6>🔍 Debug Log:</h6>
                    <div id="debug-output">
                        <div class="log-entry log-info">Sistema de debug iniciado...</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>Estado Atual:</h6>
                    <div id="estado-atual" class="alert alert-info">
                        Aguardando testes...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // Sistema de debug
    const debugOutput = document.getElementById('debug-output');
    const estadoAtual = document.getElementById('estado-atual');

    function addDebugLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
        debugOutput.appendChild(logEntry);
        debugOutput.scrollTop = debugOutput.scrollHeight;
    }

    function updateEstado(message, type = 'info') {
        estadoAtual.className = `alert alert-${type}`;
        estadoAtual.innerHTML = message;
    }

    // Interceptar console.log
    const originalLog = console.log;
    console.log = function(...args) {
        const message = args.join(' ');
        if (message.includes('🔍') || message.includes('🎯') || message.includes('⚠️') || message.includes('✅')) {
            addDebugLog(message, 'info');
        }
        originalLog.apply(console, args);
    };

    // Função de validação CPF (copiada do sistema)
    function validarCPF(cpf) {
        cpf = cpf.replace(/[^\d]+/g, '');
        if (cpf === '') return false;

        if (cpf.length !== 11 ||
            cpf === "00000000000" ||
            cpf === "11111111111" ||
            cpf === "22222222222" ||
            cpf === "33333333333" ||
            cpf === "44444444444" ||
            cpf === "55555555555" ||
            cpf === "66666666666" ||
            cpf === "77777777777" ||
            cpf === "88888888888" ||
            cpf === "99999999999")
            return false;

        let add = 0;
        for (let i = 0; i < 9; i++)
            add += parseInt(cpf.charAt(i)) * (10 - i);
        let rev = 11 - (add % 11);
        if (rev === 10 || rev === 11)
            rev = 0;
        if (rev !== parseInt(cpf.charAt(9)))
            return false;

        add = 0;
        for (let i = 0; i < 10; i++)
            add += parseInt(cpf.charAt(i)) * (11 - i);
        rev = 11 - (add % 11);
        if (rev === 10 || rev === 11)
            rev = 0;
        if (rev !== parseInt(cpf.charAt(10)))
            return false;

        return true;
    }

    // Configurar validação no formulário de teste
    function configurarValidacaoTeste() {
        const form = document.getElementById('testForm');
        const cpfField = form.querySelector('input[name="cpf"]');
        const feedbackDiv = document.getElementById('cpf-feedback');

        addDebugLog('Configurando validação de teste...', 'info');
        addDebugLog(`Campo CPF: ${cpfField ? 'Encontrado' : 'NÃO encontrado'}`, cpfField ? 'success' : 'error');
        addDebugLog(`Div feedback: ${feedbackDiv ? 'Encontrado' : 'NÃO encontrado'}`, feedbackDiv ? 'success' : 'error');

        if (cpfField && feedbackDiv) {
            cpfField.addEventListener('input', function() {
                addDebugLog(`Input event disparado: "${this.value}"`, 'info');
                
                if (!this.value) {
                    this.value = '';
                    return;
                }
                
                // Formatação
                let cpf = this.value.replace(/\D/g, '');
                if (cpf.length <= 11) {
                    cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                    cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                    cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
                }
                this.value = cpf;

                // Validação
                if (cpf.length < 14 && cpf.length > 0) {
                    addDebugLog('CPF incompleto detectado', 'warning');
                    feedbackDiv.innerHTML = '<b>CPF incompleto</b>. Digite todos os dígitos.';
                    feedbackDiv.style.color = 'orange';
                    updateEstado('CPF incompleto detectado', 'warning');
                } else if (cpf.length === 14) {
                    if (validarCPF(cpf)) {
                        addDebugLog('CPF válido detectado', 'success');
                        feedbackDiv.innerHTML = '<b>CPF válido</b> - verificando disponibilidade...';
                        feedbackDiv.style.color = 'green';
                        updateEstado('CPF válido detectado', 'success');
                    } else {
                        addDebugLog('CPF inválido detectado', 'error');
                        feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
                        feedbackDiv.style.color = 'red';
                        updateEstado('CPF inválido detectado', 'danger');
                    }
                } else if (cpf.length === 0) {
                    feedbackDiv.innerHTML = '';
                    updateEstado('Campo CPF vazio', 'info');
                }
            });
            
            addDebugLog('Event listener configurado com sucesso!', 'success');
        }
    }

    // Função para testar CPF específico
    function testarCPF(cpf) {
        const cpfField = document.querySelector('input[name="cpf"]');
        if (cpfField) {
            addDebugLog(`Testando CPF: ${cpf}`, 'info');
            cpfField.value = cpf;
            cpfField.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    function limparCPF() {
        const cpfField = document.querySelector('input[name="cpf"]');
        if (cpfField) {
            cpfField.value = '';
            cpfField.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    function abrirModalReal() {
        addDebugLog('Tentando abrir modal real do sistema...', 'info');
        if (typeof abrirModalNovoHospedeGlobal === 'function') {
            abrirModalNovoHospedeGlobal();
        } else {
            addDebugLog('Função abrirModalNovoHospedeGlobal não encontrada!', 'error');
            alert('Esta página é apenas para debug. Acesse o sistema real para testar o modal.');
        }
    }

    // Inicializar quando a página carregar
    document.addEventListener('DOMContentLoaded', function() {
        addDebugLog('Página carregada, configurando validação...', 'info');
        configurarValidacaoTeste();
        updateEstado('Sistema pronto para testes', 'success');
    });
    </script>
</body>
</html>
