<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste CPF Modal Global</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .error-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        .error-item {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 3px;
        }
        .error-item.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .error-item.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error-item.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">Teste de Correção - CPF Modal Global</h1>
        
        <div class="alert alert-info">
            <h5>Problema Identificado:</h5>
            <p><strong>Erro:</strong> <code>Cannot read properties of undefined (reading 'replace')</code></p>
            <p><strong>Linha:</strong> index.php:275 (agora corrigida)</p>
            <p><strong>Causa:</strong> <code>this.value</code> estava undefined no event listener do campo CPF</p>
        </div>

        <div class="alert alert-success">
            <h5>Correções Aplicadas:</h5>
            <ul>
                <li>✅ Adicionada verificação de segurança para <code>this.value</code></li>
                <li>✅ Adicionada verificação de tipo para parâmetros das funções</li>
                <li>✅ Corrigidas referências de contexto (<code>this</code>) nas funções</li>
                <li>✅ Criada referência à instância da classe para uso nos event listeners</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>Teste do Modal Global</h5>
            </div>
            <div class="card-body">
                <p>Clique no botão abaixo para abrir o modal global e testar o campo CPF:</p>
                <button class="btn btn-primary" onclick="abrirModalNovoHospedeGlobal()">
                    <i class="bi bi-person-plus"></i> Abrir Modal Global
                </button>
                
                <div class="mt-3">
                    <h6>Instruções para Teste:</h6>
                    <ol>
                        <li>Abra o modal clicando no botão acima</li>
                        <li>Localize o campo CPF no formulário</li>
                        <li>Digite alguns números no campo</li>
                        <li>Saia do campo (blur) e volte</li>
                        <li>Verifique se não há erros no console</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="error-log">
            <h6>Log de Erros do Console:</h6>
            <div id="console-log">
                <div class="error-item info">Aguardando testes...</div>
            </div>
        </div>
    </div>

    <!-- Modal Global (simulado para teste) -->
    <div class="modal fade" id="modalGlobalNovoHospede" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cadastrar Novo Hóspede</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalGlobalNovoHospedeContainer">
                    <!-- Formulário simulado para teste -->
                    <form id="formHospedeCompleto">
                        <div class="form-group mb-3">
                            <label for="nome">Nome:</label>
                            <input type="text" name="nome" class="form-control" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="cpf">CPF:</label>
                            <input type="text" name="cpf" class="form-control">
                            <div id="cpf-feedback" class="invalid-feedback">
                                <b>CPF inválido</b>.
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <button type="submit" class="btn btn-primary">Registrar</button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // Interceptar erros do console
    const originalError = console.error;
    const originalLog = console.log;
    const logContainer = document.getElementById('console-log');

    function addToLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logItem = document.createElement('div');
        logItem.className = `error-item ${type}`;
        logItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
        logContainer.appendChild(logItem);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    console.error = function(...args) {
        addToLog(`ERROR: ${args.join(' ')}`, 'error');
        originalError.apply(console, args);
    };

    console.log = function(...args) {
        if (args[0] && args[0].includes && (args[0].includes('CPF') || args[0].includes('validar'))) {
            addToLog(`LOG: ${args.join(' ')}`, 'info');
        }
        originalLog.apply(console, args);
    };

    // Interceptar erros JavaScript
    window.addEventListener('error', function(e) {
        addToLog(`JS ERROR: ${e.message} (${e.filename}:${e.lineno})`, 'error');
    });

    // Função simulada para abrir modal (para teste)
    function abrirModalNovoHospedeGlobal() {
        addToLog('Tentando abrir modal global...', 'info');
        
        // Simular abertura do modal
        const modal = new bootstrap.Modal(document.getElementById('modalGlobalNovoHospede'));
        modal.show();
        
        // Simular configuração do formulário após um delay
        setTimeout(() => {
            addToLog('Configurando validações do formulário...', 'info');
            configurarValidacoesTeste();
        }, 500);
    }

    // Função de teste simplificada baseada na correção
    function configurarValidacoesTeste() {
        const form = document.getElementById('formHospedeCompleto');
        const cpfField = form.querySelector('input[name="cpf"]');
        
        if (cpfField) {
            addToLog('Campo CPF encontrado, configurando event listeners...', 'success');
            
            cpfField.addEventListener('input', function() {
                addToLog('Event listener do CPF executado', 'success');
                
                // Verificação de segurança (correção aplicada)
                if (!this.value) {
                    this.value = '';
                    addToLog('Valor do campo estava undefined/null - corrigido!', 'success');
                    return;
                }
                
                // Formatação
                let cpf = this.value.replace(/\D/g, '');
                if (cpf.length <= 11) {
                    cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                    cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                    cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
                }
                this.value = cpf;
                
                addToLog(`CPF formatado: ${cpf}`, 'success');
            });
            
            addToLog('Event listeners configurados com sucesso!', 'success');
        } else {
            addToLog('Campo CPF não encontrado!', 'error');
        }
    }

    // Log inicial
    document.addEventListener('DOMContentLoaded', function() {
        addToLog('Página carregada. Pronto para testes.', 'info');
    });
    </script>
</body>
</html>
