<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Modal - Compatibilidade Navegadores</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="custom/css/form_fnrh.css">
    <link rel="stylesheet" href="custom/css/mapa_uh.css">
    <link rel="stylesheet" href="custom/css/responsive-fixes.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Teste de Modal - Compatibilidade</h1>
        
        <div class="text-center">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testeModal">
                Abrir Modal de Teste
            </button>
        </div>
        
        <div class="mt-3">
            <p><strong>Navegador:</strong> <span id="navegadorInfo"></span></p>
            <p><strong>Versão:</strong> <span id="versaoInfo"></span></p>
        </div>
    </div>

    <!-- Modal de Teste -->
    <div class="modal fade" id="testeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Detalhes da Unidade Habitacional</h5>
                    <div class="navigation-buttons">
                        <button type="button" class="btn btn-outline-secondary btn-sm" title="Data anterior">
                            <i class="bi bi-arrow-left"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" title="Próxima data">
                            <i class="bi bi-arrow-right"></i>
                        </button>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><strong>UH:</strong> <span>002</span></p>
                    <p><strong>Data:</strong> <span>16/08/2025</span></p>
                    
                    <div class="mb-3">
                        <h6>Reservas do Dia</h6>
                        <div class="reserva-item border rounded p-3 mb-2">
                            <h6>FÁTIMA LETÍCIA GONÇALVES</h6>
                            <p><strong>Entrada:</strong> 15/08/2025 às 14:00</p>
                            <p><strong>Saída:</strong> 17/08/2025 às 12:00</p>
                            <p><strong>Acompanhantes:</strong> 1</p>
                            <p><strong>Valor:</strong> R$ 1000,00</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success">Nova Reserva</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Detectar navegador
        function detectarNavegador() {
            const userAgent = navigator.userAgent;
            let navegador = "Desconhecido";
            let versao = "Desconhecida";
            
            if (userAgent.indexOf("Firefox") > -1) {
                navegador = "Mozilla Firefox";
                versao = userAgent.match(/Firefox\/([0-9.]+)/)[1];
            } else if (userAgent.indexOf("Edg") > -1) {
                navegador = "Microsoft Edge";
                versao = userAgent.match(/Edg\/([0-9.]+)/)[1];
            } else if (userAgent.indexOf("Chrome") > -1) {
                navegador = "Google Chrome";
                versao = userAgent.match(/Chrome\/([0-9.]+)/)[1];
            } else if (userAgent.indexOf("Safari") > -1) {
                navegador = "Safari";
                versao = userAgent.match(/Version\/([0-9.]+)/)[1];
            }
            
            document.getElementById('navegadorInfo').textContent = navegador;
            document.getElementById('versaoInfo').textContent = versao;
        }
        
        // Executar quando a página carregar
        document.addEventListener('DOMContentLoaded', function() {
            detectarNavegador();
            
            // Adicionar logs para debug
            const modal = document.getElementById('testeModal');
            modal.addEventListener('show.bs.modal', function() {
                console.log('Modal está sendo exibido');
            });
            
            modal.addEventListener('shown.bs.modal', function() {
                console.log('Modal foi exibido completamente');
                
                // Verificar se header e footer estão visíveis
                const header = modal.querySelector('.modal-header');
                const footer = modal.querySelector('.modal-footer');
                
                console.log('Header visível:', window.getComputedStyle(header).display !== 'none');
                console.log('Footer visível:', window.getComputedStyle(footer).display !== 'none');
            });
        });
    </script>
</body>
</html>
