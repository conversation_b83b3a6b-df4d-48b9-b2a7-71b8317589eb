<?php
// Limpar qualquer output buffer existente
if (ob_get_level()) {
    ob_end_clean();
}

// Iniciar output buffering para capturar qualquer output indesejado
ob_start();

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {
    echo json_encode(['success' => false, 'message' => 'Sessão inválida']);
    exit;
}

include_once("config.php");

$hospede_id = $_POST['hospede_id'] ?? '';

if (empty($hospede_id)) {
    echo json_encode(['success' => false, 'message' => 'ID do hóspede não fornecido']);
    exit;
}

$pousada_id = $_SESSION['user_pousada_id'];

// Buscar dados do hóspede
$sql = "SELECT * FROM hospedes WHERE id = ? AND pousada_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('ii', $hospede_id, $pousada_id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_object();

if (!$row) {
    echo json_encode(['success' => false, 'message' => 'Hóspede não encontrado']);
    exit;
}

// Configurar variáveis para o formulário
$ajax_context = true;
$form_action = 'hospedes_salvar.php';
$form_id = 'formEditarHospede';
$hidden_fields = '<input type="hidden" name="acao" value="editar"><input type="hidden" name="id" value="' . htmlspecialchars($row->id) . '"><input type="hidden" name="ajax" value="1">';

// Preencher valores dos campos
$nome = $row->nome;
$nasc = $row->nasc;
$idade = $row->idade;
$profissao = $row->profissao;
$nacionalidade = $row->nacionalidade;
$sexo = $row->sexo;
$cpf = $row->cpf;
$documento = $row->documento;
$tipo = $row->tipo;
$expedidor = $row->expedidor;
$endereco = $row->endereco;
$telefone = $row->telefone;
$cep = $row->cep;
$cidade = $row->cidade;
$uf = $row->uf;
$pais = $row->pais;
$email = $row->email;
$titulo_hospede = 'Editar Hóspede:';
$button_text = 'Salvar Alterações';

// Capturar o HTML do formulário
ob_start();
try {
    include 'formulario_hospede.php';
    $formulario_html = ob_get_clean();
} catch (Exception $e) {
    ob_end_clean();
    echo json_encode(['success' => false, 'message' => 'Erro ao carregar formulário: ' . $e->getMessage()]);
    exit;
}

// Obter nome da pousada
$nome_pousada = $_SESSION['pousada_nome'] ?? 'Pousada Bom Viver';

// Cabeçalho para impressão (início da página)
$print_header = '
<div class="print-header print-only">
    <div class="print-header-container">
        <div class="print-logo-container">
            <img src="' . ($_SESSION['pousada_logo'] ?? 'img/Logo_Bom_Viver.png') . '" alt="Logo" class="print-logo">
        </div>
        <div class="print-info-container">
            <h2 class="print-pousada-nome">' . htmlspecialchars($nome_pousada) . '</h2>
            <h3 class="print-documento-tipo">Cadastro de Hóspede</h3>
        </div>
    </div>
    <hr class="print-separator">
</div>';

// Elementos de impressão (botão e QR code)
$print_elements = '
<div class="form-group no-print mt-3">
    <center>
    <button type="button" class="btn btn-secondary" id="print-button-hospede">
        <i class="bi bi-printer"></i> Imprimir Cadastro
    </button>
    </center>
</div>

<center class="print-only">
    <br>
    <div id="qrcode-hospede"></div>
    <br>
    <p><small>QR Code do Hóspede: ' . htmlspecialchars($row->id) . '</small></p>
</center>';

$html = $print_header . $formulario_html . $print_elements;

// Verificar se o HTML é válido antes de enviar
if (empty($formulario_html)) {
    echo json_encode(['success' => false, 'message' => 'Formulário vazio ou erro ao gerar HTML']);
    exit;
}

// Capturar e descartar qualquer output indesejado
$unwanted_output = ob_get_clean();
if (!empty($unwanted_output)) {
    error_log("Output indesejado capturado: " . $unwanted_output);
}

// Garantir que não há output antes do JSON
header('Content-Type: application/json; charset=utf-8');

$response = [
    'success' => true,
    'html' => $html,
    'hospede_id' => $hospede_id
];

echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;
?>
